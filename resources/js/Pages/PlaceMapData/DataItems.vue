<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { ref, onMounted, computed, defineComponent, h, watch } from 'vue';
import axios from 'axios';

// Skeleton component
const Skeleton = defineComponent({
    props: {
        class: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        return () => h('div', { class: `animate-pulse bg-gray-200 rounded-md ${props.class}` });
    }
});

// Reusable Shadcn-style Alert Component
const Alert = defineComponent({
    props: ['type', 'message'],
    emits: ['close'],
    setup(props, { emit }) {
        const alertClasses = computed(() => {
            const base = 'fixed bottom-6 left-6 z-[100] w-full max-w-sm rounded-lg border p-4 shadow-lg flex items-start';
            let variantClasses = '';
            if (props.type === 'success') {
                variantClasses = 'border-green-300 bg-green-50 text-green-800';
            } else if (props.type === 'error') {
                variantClasses = 'border-red-300 bg-red-50 text-red-800';
            } else {
                variantClasses = 'border-gray-300 bg-gray-50 text-gray-800';
            }
            return `${base} ${variantClasses}`;
        });

        const iconSvg = computed(() => {
            if (props.type === 'success') {
                return h('svg', { class: 'h-5 w-5 text-green-500', fill: 'none', viewBox: '0 0 24 24', stroke: 'currentColor', 'stroke-width': '2' }, [
                    h('path', { 'stroke-linecap': 'round', 'stroke-linejoin': 'round', d: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' })
                ]);
            }
            if (props.type === 'error') {
                return h('svg', { class: 'h-5 w-5 text-red-500', fill: 'none', viewBox: '0 0 24 24', stroke: 'currentColor', 'stroke-width': '2' }, [
                    h('path', { 'stroke-linecap': 'round', 'stroke-linejoin': 'round', d: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' })
                ]);
            }
            return null;
        });

        return () => h('div', { class: alertClasses.value, role: 'alert' }, [
            iconSvg.value ? h('div', { class: 'flex-shrink-0' }, [iconSvg.value]) : null,
            h('div', { class: 'ml-3 flex-1' }, [
                h('p', { class: 'text-sm font-medium' }, props.message)
            ]),
            h('button', {
                type: 'button',
                class: 'ml-auto -mx-1.5 -my-1.5 bg-transparent rounded-md p-1.5 inline-flex items-center justify-center text-gray-500 hover:text-gray-800 focus:outline-none',
                onClick: () => emit('close')
            }, [
                h('span', { class: 'sr-only' }, 'Dismiss'),
                h('svg', { class: 'h-4 w-4', fill: 'currentColor', viewBox: '0 0 20 20' }, [
                    h('path', { 'fill-rule': 'evenodd', d: 'M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z', 'clip-rule': 'evenodd' })
                ])
            ])
        ]);
    }
});

// Shadcn-like components
const Button = 'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
const ButtonPrimary = `${Button} bg-black text-white hover:bg-gray-800 shadow-md`;
const ButtonSecondary = `${Button} bg-gray-200 text-gray-800 hover:bg-gray-300`;
const ButtonDestructive = `${Button} bg-red-600 text-white hover:bg-red-700`;

const Input = 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50';
const Label = 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70';
const Select = `${Input}`;

// Props
const props = defineProps({
    placeMapId: {
        type: String,
        required: true,
    },
    dataMapId: {
        type: String,
        required: true,
    }
});

// State
const dataMap = ref(null);
const placeMapItems = ref([]);
const dataMapItems = ref([]);
const itemsPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const alert = ref({ show: false, type: '', message: '' });

const showCreateEditItemModal = ref(false);
const isEditingItem = ref(false);
const editingItemId = ref(null);
const itemForm = ref({
    name: '',
    description: '',
    placeMapItemID: '',
    type: 'single', // Added type field
    dataItems: [],
    processing: false,
    errors: {}
});

const customFields = computed(() => {
    if (dataMap.value && dataMap.value.customFields) {
        try {
            const fields = typeof dataMap.value.customFields === 'string'
                ? JSON.parse(dataMap.value.customFields)
                : dataMap.value.customFields;
            return Array.isArray(fields) ? fields : [];
        } catch (e) {
            console.error('Error parsing custom fields:', e);
            return [];
        }
    }
    return [];
});

const getFieldInputType = (fieldName) => {
    const field = customFields.value.find(f => f.name === fieldName);
    if (field) {
        if (field.type === 'number') return 'number';
        if (field.type === 'date') return 'date';
    }
    return 'text';
};

// Watcher for itemForm.type to dynamically adjust dataItems
watch(() => itemForm.value.type, (newType) => {
    if (newType === 'single') {
        // If switching from multi to single, flatten existing dataItems and map to custom fields
        const existingDataMap = itemForm.value.dataItems.flat().reduce((acc, curr) => {
            acc[curr.name] = curr.value;
            return acc;
        }, {});
        itemForm.value.dataItems = customFields.value.map(f => ({
            name: f.name,
            value: existingDataMap[f.name] || ''
        }));
    } else if (newType === 'multi') {
        // If switching from single to multi, clear and prepare for new entries
        // If there are existing single data items, convert them to a multi entry
        if (itemForm.value.dataItems.length > 0 && !Array.isArray(itemForm.value.dataItems[0])) {
            itemForm.value.dataItems = [itemForm.value.dataItems];
        } else {
            itemForm.value.dataItems = []; // Clear if already multi or empty
        }
    }
}, { immediate: true });

// Functions
const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => alert.value.show = false, duration);
};

const fetchDataMapDetails = async () => {
    try {
        const response = await axios.get(route('dataMap.getById', { placeMapId: props.placeMapId, dataMapId: props.dataMapId }));
        dataMap.value = response.data;
    } catch (error) {
        showAlert('error', 'Error fetching data map details.');
        console.error('Error fetching data map details:', error);
    }
};

const fetchPlaceMapItems = async () => {
    try {
        const response = await axios.get(route('placeMap.getPlaceMapItem', { placeMapId: props.placeMapId, perPage: 9999 })); // Fetch all for dropdown
        placeMapItems.value = response.data.data;
    } catch (error) {
        showAlert('error', 'Error fetching place map items for dropdown.');
        console.error('Error fetching place map items:', error);
    }
};

const fetchDataMapItems = async (page = 1, perPage = itemsPerPage.value) => {
    try {
        const response = await axios.get(route('dataMap.getDataMapItem', { dataMapId: props.dataMapId, page, perPage: perPage }));
        dataMapItems.value = response.data.data;
        itemsPagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.perPage,
        };
    } catch (error) {
        showAlert('error', 'Error fetching data map items.');
        console.error('Error fetching data map items:', error);
    }
};

onMounted(async () => {
    await fetchDataMapDetails();
    await fetchPlaceMapItems();
    await fetchDataMapItems();
});

const openCreateItemModal = () => {
    isEditingItem.value = false;
    editingItemId.value = null;
    itemForm.value = {
        name: '',
        description: '',
        placeMapItemID: '',
        type: 'single', // Default to single
        dataItems: customFields.value.map(f => ({ name: f.name, value: '' })), // Initialize for single type
        processing: false,
        errors: {}
    };
    showCreateEditItemModal.value = true;
};

const openEditItemModal = (item) => {
    isEditingItem.value = true;
    editingItemId.value = item.id;

    let existingDataItems = [];
    if (item.dataItems) {
        if (typeof item.dataItems === 'string' && item.dataItems.trim() !== '') {
            try {
                const parsed = JSON.parse(item.dataItems);
                if (Array.isArray(parsed) && parsed.every(Array.isArray)) {
                    existingDataItems = parsed.flat();
                } else {
                    existingDataItems = parsed;
                }
            } catch (e) {
                console.error('Error parsing item.dataItems JSON:', e, 'Data:', item.dataItems);
                existingDataItems = [];
            }
        } else if (Array.isArray(item.dataItems)) {
            if (item.dataItems.every(Array.isArray)) {
                existingDataItems = item.dataItems.flat();
            } else {
                existingDataItems = item.dataItems;
            }
        }
    }
    const existingDataMap = existingDataItems.reduce((acc, curr) => {
        acc[curr.name] = curr.value;
        return acc;
    }, {});

    itemForm.value = {
        name: item.name,
        description: item.description,
        placeMapItemID: item.place_map_item_id,
        type: item.type || 'single', // Set type from item or default to 'single'
        dataItems: item.type === 'multi' ? existingDataItems : customFields.value.map(f => ({
            name: f.name,
            value: existingDataMap[f.name] || ''
        })),
        processing: false,
        errors: {}
    };
    showCreateEditItemModal.value = true;
};

const submitDataItem = async () => {
    itemForm.value.processing = true;
    itemForm.value.errors = {};
    
    const dataMapId = props.dataMapId;
    const url = isEditingItem.value
        ? route('dataMap.updateDataItem', { dataMapId, dataMapItemId: editingItemId.value })
        : route('dataMap.createDataItem', { dataMapId });

    const method = isEditingItem.value ? 'put' : 'post';

    let payload = { ...itemForm.value };
    if (payload.type === 'multi') {
        payload.dataItems = payload.dataItems.flat();
    }

    try {
        const response = await axios[method](url, payload);
        showAlert('success', response.data.message);
        await fetchDataMapItems(itemsPagination.value?.currentPage || 1, itemsPerPage.value);
        showCreateEditItemModal.value = false;
    } catch (error) {
        const errorMessage = error.response?.data?.message || 'An unexpected error occurred.';
        showAlert('error', errorMessage);
        if (error.response && error.response.status === 422 && error.response.data.errors) {
            itemForm.value.errors = error.response.data.errors;
        } else {
            console.error('An unexpected error occurred:', error);
        }
    } finally {
        itemForm.value.processing = false;
    }
};

const addDataItem = () => {
    itemForm.value.dataItems.push(customFields.value.map(f => ({ name: f.name, value: '' })));
};

const removeDataItem = (index) => {
    itemForm.value.dataItems.splice(index, 1);
};
</script>

<template>
    <AppLayout :title="dataMap ? `Items in ${dataMap.name}` : 'Data Map Items'">
        <Alert v-if="alert.show" :type="alert.type" :message="alert.message" @close="alert.show = false" />
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div v-if="dataMap" class="bg-white overflow-hidden shadow-xl border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <div class="mb-6">
                        <Link :href="route('myMapData.index', { placeMapId: placeMapId })" class="text-sm text-indigo-600 hover:underline mb-4 inline-block">&larr; Back to Data Maps</Link>
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Items in {{ dataMap.name }}</h1>
                                <p class="mt-1 text-md text-gray-600">{{ dataMap.description }}</p>
                            </div>
                            <button @click="openCreateItemModal" :class="`${ButtonPrimary} px-4 py-2 self-start md:self-center`">Add New Data Item</button>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place Map Item</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr v-for="item in dataMapItems" :key="item.id" class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.place_map_item?.name || 'N/A' }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button @click="openEditItemModal(item)" class="font-medium text-indigo-600 hover:text-indigo-800 hover:underline">Edit</button>
                                    </td>
                                 </tr>
                                <tr v-if="dataMapItems.length === 0">
                                    <td colspan="3" class="text-center text-gray-500 py-12">
                                        <p class="text-lg">No data items found for this map.</p>
                                        <p class="text-sm">Get started by adding a new data item.</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div v-if="itemsPagination && itemsPagination.total > 0" class="mt-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>Per Page:</span>
                            <select id="itemsPerPage" v-model="itemsPerPage" @change="fetchDataMapItems(1, $event.target.value)" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1">
                                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }}</option>
                            </select>
                            <span class="hidden sm:inline">| Total Items: {{ itemsPagination.total }}</span>
                        </div>
                        <nav v-if="itemsPagination.lastPage > 1" class="flex items-center justify-center">
                            <button @click="fetchDataMapItems(itemsPagination.currentPage - 1, itemsPerPage)" :disabled="itemsPagination.currentPage <= 1" 
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                &laquo; Prev
                            </button>
                            <span class="px-4 py-1 text-sm text-gray-800 bg-gray-100 border-t border-b border-gray-300">
                                Page {{ itemsPagination.currentPage }} of {{ itemsPagination.lastPage }}
                            </span>
                            <button @click="fetchDataMapItems(itemsPagination.currentPage + 1, itemsPerPage)" :disabled="itemsPagination.currentPage >= itemsPagination.lastPage"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Next &raquo;
                            </button>
                        </nav>
                    </div>
                </div>
                <div v-else class="bg-white overflow-hidden shadow-xl border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <!-- Skeleton Loader -->
                    <div class="mb-6">
                        <Skeleton class="h-4 w-1/4 mb-4" />
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <Skeleton class="h-8 w-48 mb-2" />
                                <Skeleton class="h-4 w-64" />
                            </div>
                            <Skeleton class="h-10 w-32" />
                        </div>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div class="py-4" v-for="i in 5" :key="i">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 space-y-2">
                                    <Skeleton class="h-4 w-1/3" />
                                </div>
                                <div class="flex items-center space-x-4">
                                    <Skeleton class="h-8 w-16" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showCreateEditItemModal" class="fixed inset-0 bg-black bg-opacity-50 z-[60] flex items-center justify-center p-4">
             <div class="bg-white rounded-lg shadow-2xl p-8 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <h2 class="text-2xl font-bold mb-6">{{ isEditingItem ? 'Edit' : 'Create' }} Data Item</h2>
                <form @submit.prevent="submitDataItem">
                    <div class="space-y-4">
                        <div>
                            <label :class="Label" for="itemName">Item Name</label>
                            <input id="itemName" v-model="itemForm.name" placeholder="Item Name" :class="Input" required>
                            <div v-if="itemForm.errors.name" class="text-red-500 text-sm mt-1">{{ itemForm.errors.name[0] }}</div>
                        </div>
                        <div>
                            <label :class="Label" for="itemDescription">Description</label>
                            <textarea id="itemDescription" v-model="itemForm.description" placeholder="Description" :class="Input" rows="2"></textarea>
                            <div v-if="itemForm.errors.description" class="text-red-500 text-sm mt-1">{{ itemForm.errors.description[0] }}</div>
                        </div>
                        <div>
                            <label :class="Label" for="placeMapItem">Associate with Place Map Item</label>
                            <select id="placeMapItem" v-model="itemForm.placeMapItemID" :class="Select" required>
                                <option value="">Select a Place Map Item</option>
                                <option v-for="placeItem in placeMapItems" :key="placeItem.id" :value="placeItem.id">{{ placeItem.name }}</option>
                            </select>
                            <div v-if="itemForm.errors.placeMapItemID" class="text-red-500 text-sm mt-1">{{ itemForm.errors.placeMapItemID[0] }}</div>
                        </div>
                        <div>
                            <label :class="Label" for="itemType">Type</label>
                            <select id="itemType" v-model="itemForm.type" :class="Select" required>
                                <option value="single">Single</option>
                                <option value="multi">Multi</option>
                            </select>
                            <div v-if="itemForm.errors.type" class="text-red-500 text-sm mt-1">{{ itemForm.errors.type[0] }}</div>
                        </div>

                        <div v-if="itemForm.type === 'single' && customFields.length > 0">
                            <h3 class="font-semibold mb-2">Custom Data</h3>
                            <div v-for="(item, index) in itemForm.dataItems" :key="index" class="mb-2">
                                <label :class="Label">{{ item.name }}</label>
                                <input v-model="item.value" :class="Input" :type="getFieldInputType(item.name)">
                                <div v-if="itemForm.errors[`dataItems.${index}.value`]" class="text-red-500 text-sm mt-1">{{ itemForm.errors[`dataItems.${index}.value`][0] }}</div>
                            </div>
                        </div>

                        <div v-if="itemForm.type === 'multi'">
                            <h3 class="font-semibold mb-2">Data Entries</h3>
                            <div v-for="(dataEntry, entryIndex) in itemForm.dataItems" :key="entryIndex" class="mb-4 p-4 border rounded-md bg-gray-50">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="font-medium">Entry #{{ entryIndex + 1 }}</h4>
                                    <button type="button" @click="removeDataItem(entryIndex)" :class="`${ButtonDestructive} px-3 py-1`">Remove Entry</button>
                                </div>
                                <div class="space-y-2">
                                    <div v-for="(field, fieldIndex) in dataEntry" :key="fieldIndex">
                                        <label :class="Label">{{ field.name }}</label>
                                        <input v-model="field.value" :class="Input" :type="getFieldInputType(field.name)" required>
                                        <div v-if="itemForm.errors[`dataItems.${entryIndex}.${fieldIndex}.value`]" class="text-red-500 text-sm mt-1">{{ itemForm.errors[`dataItems.${entryIndex}.${fieldIndex}.value`][0] }}</div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" @click="addDataItem" :class="`${ButtonSecondary} px-4 py-2`">Add New Entry</button>
                        </div>
                    </div>
                     <div class="mt-6 flex justify-end space-x-4">
                        <button type="button" @click="showCreateEditItemModal = false" :class="`${ButtonSecondary} px-4 py-2`">Cancel</button>
                        <button type="submit" :disabled="itemForm.processing" :class="`${ButtonPrimary} px-4 py-2`">{{ itemForm.processing ? 'Saving...' : (isEditingItem ? 'Update' : 'Create') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
