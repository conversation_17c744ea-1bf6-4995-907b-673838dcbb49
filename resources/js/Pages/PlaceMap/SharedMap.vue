<script setup>
import { ref, onMounted, onUnmounted, defineComponent, h } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import axios from 'axios';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps({
    mapKey: {
        type: String,
        required: true,
    },
});

// --- STATE REFS ---
const mapContainer = ref(null);
const map = ref(null);
const mapData = ref(null);
const isLoading = ref(true);
const error = ref(null);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Light');

// Route calculation state
const routeMode = ref(false);
const routePoints = ref([]);
const routeData = ref(null);
const routeLoading = ref(false);
const routeError = ref(null);

// --- CONFIGURATION ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403], // Default center for Rwanda
    zoom: 8,
    fitBoundsPadding: { top: 100, bottom: 100, left: 100, right: 100 },
};

const ROUTE_API_CONFIG = {
    baseUrl: 'http://127.0.0.1:5000',
    profile: 'driving', // driving, walking, cycling
};

const UI_CONFIG = {
    mapThemes: {
        'Light': {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.2, 'raster-contrast': 0.1, 'raster-opacity': 0.9 }
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            paint: {}
        }
    },
};

// --- DYNAMIC ICON COMPONENT ---
const SvgIcon = defineComponent({
    props: ['name', 'size'],
    setup(props) {
        const size = props.size || 24;
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
            route: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>`,
            clear: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>`,
        };
        return () => h('svg', {
            width: size,
            height: size,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            innerHTML: iconPaths[props.name] || iconPaths.pin
        });
    }
});

// --- ROUTE FUNCTIONS ---
const toggleRouteMode = () => {
    routeMode.value = !routeMode.value;
    if (!routeMode.value) {
        clearRoute();
    }
};

const clearRoute = () => {
    routePoints.value = [];
    routeData.value = null;
    routeError.value = null;
    
    // Remove route layers and sources from map
    if (map.value) {
        if (map.value.getSource('route')) {
            map.value.removeLayer('route');
            map.value.removeSource('route');
        }
        if (map.value.getSource('route-points')) {
            map.value.removeLayer('route-points');
            map.value.removeSource('route-points');
        }
    }
};

const calculateRoute = async () => {
    if (routePoints.value.length < 2) return;
    
    routeLoading.value = true;
    routeError.value = null;
    
    try {
        // Format coordinates for the API: lng,lat;lng,lat
        const coordinates = routePoints.value
            .map(point => `${point.lng},${point.lat}`)
            .join(';');
        
        const url = `${ROUTE_API_CONFIG.baseUrl}/route/v1/${ROUTE_API_CONFIG.profile}/${coordinates}?steps=true`;
        
        const response = await axios.get(url);
        
        if (response.data.code === 'Ok' && response.data.routes.length > 0) {
            routeData.value = response.data.routes[0];
            displayRoute();
        } else {
            throw new Error('No route found');
        }
    } catch (err) {
        console.error('Route calculation error:', err);
        routeError.value = 'Failed to calculate route. Please check your local routing service.';
    } finally {
        routeLoading.value = false;
    }
};

const displayRoute = () => {
    if (!map.value || !routeData.value) return;
    
    // Decode the geometry (polyline format)
    const coordinates = decodePolyline(routeData.value.geometry);
    
    // Remove existing route if present
    if (map.value.getSource('route')) {
        map.value.removeLayer('route');
        map.value.removeSource('route');
    }
    
    // Add route source and layer
    map.value.addSource('route', {
        type: 'geojson',
        data: {
            type: 'Feature',
            properties: {},
            geometry: {
                type: 'LineString',
                coordinates: coordinates
            }
        }
    });
    
    map.value.addLayer({
        id: 'route',
        type: 'line',
        source: 'route',
        layout: {
            'line-join': 'round',
            'line-cap': 'round'
        },
        paint: {
            'line-color': '#3b82f6',
            'line-width': 5,
            'line-opacity': 0.8
        }
    });
    
    // Add route points
    if (map.value.getSource('route-points')) {
        map.value.removeLayer('route-points');
        map.value.removeSource('route-points');
    }
    
    map.value.addSource('route-points', {
        type: 'geojson',
        data: {
            type: 'FeatureCollection',
            features: routePoints.value.map((point, index) => ({
                type: 'Feature',
                properties: {
                    type: index === 0 ? 'start' : index === routePoints.value.length - 1 ? 'end' : 'waypoint'
                },
                geometry: {
                    type: 'Point',
                    coordinates: [point.lng, point.lat]
                }
            }))
        }
    });
    
    map.value.addLayer({
        id: 'route-points',
        type: 'circle',
        source: 'route-points',
        paint: {
            'circle-radius': [
                'case',
                ['==', ['get', 'type'], 'start'], 8,
                ['==', ['get', 'type'], 'end'], 8,
                6
            ],
            'circle-color': [
                'case',
                ['==', ['get', 'type'], 'start'], '#10b981',
                ['==', ['get', 'type'], 'end'], '#ef4444',
                '#f59e0b'
            ],
            'circle-stroke-width': 2,
            'circle-stroke-color': '#ffffff'
        }
    });
    
    // Fit map to route bounds
    const bounds = new maplibregl.LngLatBounds();
    coordinates.forEach(coord => bounds.extend(coord));
    map.value.fitBounds(bounds, { padding: 50 });
};

const handleMapClick = (e) => {
    if (!routeMode.value) return;
    
    const point = { lng: e.lngLat.lng, lat: e.lngLat.lat };
    routePoints.value.push(point);
    
    if (routePoints.value.length >= 2) {
        calculateRoute();
    }
};

// Polyline decoder (simplified version for basic polylines)
const decodePolyline = (encoded) => {
    const coordinates = [];
    let index = 0;
    let lat = 0;
    let lng = 0;
    
    while (index < encoded.length) {
        let shift = 0;
        let result = 0;
        let byte;
        
        do {
            byte = encoded.charCodeAt(index++) - 63;
            result |= (byte & 0x1F) << shift;
            shift += 5;
        } while (byte >= 0x20);
        
        const deltaLat = ((result & 1) ? ~(result >> 1) : (result >> 1));
        lat += deltaLat;
        
        shift = 0;
        result = 0;
        
        do {
            byte = encoded.charCodeAt(index++) - 63;
            result |= (byte & 0x1F) << shift;
            shift += 5;
        } while (byte >= 0x20);
        
        const deltaLng = ((result & 1) ? ~(result >> 1) : (result >> 1));
        lng += deltaLng;
        
        coordinates.push([lng / 1e5, lat / 1e5]);
    }
    
    return coordinates;
};

const formatDistance = (meters) => {
    if (meters < 1000) {
        return `${Math.round(meters)} m`;
    }
    return `${(meters / 1000).toFixed(1)} km`;
};

const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
};

// --- EXISTING LOGIC FUNCTIONS ---
const fetchMapData = async () => {
    isLoading.value = true;
    error.value = null;
    try {
        const response = await axios.get(`/map/shared/map-data/${props.mapKey}`);
        mapData.value = response.data;
        setupMapMarkers();
    } catch (err) {
        console.error('Error fetching map data:', err);
        error.value = err.response?.data?.message || 'Could not load the map data. The link may be invalid or expired.';
    } finally {
        isLoading.value = false;
    }
};

const setupMapMarkers = () => {
    if (!map.value || !mapData.value) return;

    const bounds = new maplibregl.LngLatBounds();
    mapData.value.place_map_items.forEach(item => {
        if (item.latitude && item.longitude) {
            const el = document.createElement('div');
            el.className = 'custom-marker';
            
            // Check if item has a custom image URL or use icon
            const iconContent = item.image && item.image.startsWith('http') 
                ? `<img src="${item.image}" alt="${item.name}" class="marker-custom-image" />`
                : `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                     <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/>
                     <circle cx="12" cy="9" r="2.5"/>
                     ${getIconPath(item.image || 'pin')}
                   </svg>`;
                   
            el.innerHTML = `<div class="marker-icon-container">${iconContent}</div>`;

            const popupContent = `
                <div class="p-4 max-w-sm">
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                            ${item.image && item.image.startsWith('http') 
                                ? `<img src="${item.image}" alt="${item.name}" class="w-6 h-6 object-cover rounded" />`
                                : `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="1.5">${getIconPath(item.image || 'pin')}</svg>`
                            }
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">${item.name}</h3>
                            ${item.description ? `<p class="text-sm text-gray-600 mb-2">${item.description}</p>` : ''}
                            ${item.address ? `<div class="border-t border-gray-100 pt-2 mt-2"><p class="text-xs text-gray-500 font-medium mb-1">Address</p><p class="text-sm text-gray-700">${item.address}</p></div>` : ''}
                            <div class="border-t border-gray-100 pt-2 mt-2">
                                <p class="text-xs text-gray-500 font-medium mb-1">Coordinates</p>
                                <p class="text-xs text-gray-600 font-mono">${parseFloat(item.latitude).toFixed(6)}, ${parseFloat(item.longitude).toFixed(6)}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            new maplibregl.Marker({ element: el })
                .setLngLat([item.longitude, item.latitude])
                .setPopup(new maplibregl.Popup({ offset: 35, closeButton: true }).setHTML(popupContent))
                .addTo(map.value);

            bounds.extend([item.longitude, item.latitude]);
        }
    });

    if (!bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: 16 });
    } else {
        map.value.setCenter([mapData.value.longitude || MAP_CONFIG.center[0], mapData.value.latitude || MAP_CONFIG.center[1]]);
        map.value.setZoom(mapData.value.zoom || MAP_CONFIG.zoom);
    }
};

const getIconPath = (name) => {
    const iconPaths = {
        pin: `<path d="M12 6.5a2.5 2.5 0 100 5 2.5 2.5 0 000-5z"/>`,
        star: `<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
        heart: `<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
        flag: `<path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
        home: `<path d="M9 22V12h6v10"/><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>`,
        work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>`,
        cafe: `<path d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"/>`,
        park: `<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6"/><path d="M1 12h6m6 0h6"/>`,
        restaurant: `<path d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
        shopping: `<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
        hospital: `<path d="M12 6v12"/><path d="M18 12H6"/><rect x="3" y="3" width="18" height="18" rx="2"/>`,
        school: `<path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
    };
    return iconPaths[name] || iconPaths.pin;
};

const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }

        const defaultPaint = { 'raster-saturation': 0, 'raster-contrast': 0, 'raster-opacity': 1 };
        Object.keys(defaultPaint).forEach(key => {
            map.value.setPaintProperty('osm-tiles', key, defaultPaint[key]);
        });

        const newPaint = theme.paint || {};
        Object.keys(newPaint).forEach(key => {
            map.value.setPaintProperty('osm-tiles', key, newPaint[key]);
        });

        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

// --- LIFECYCLE HOOKS ---
onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        isLoading.value = false;
        return;
    }
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Light'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { osm: { type: 'raster', tiles: [initialTheme.url], tileSize: 256, attribution: initialTheme.attribution } },
                layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: initialTheme.paint || {} }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
        });
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        map.value.on('load', fetchMapData);
        map.value.on('click', handleMapClick);
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
        isLoading.value = false;
    }
});

onUnmounted(() => {
    map.value?.remove();
});

</script>

<template>
    <AppLayout :title="mapData ? mapData.name : 'Shared Map'">
        <Head>
            <title>{{ mapData ? `${mapData.name} - Shared Map` : 'Shared Map' }}</title>
            <meta v-if="mapData" name="description" :content="mapData.description || `A shared map named ${mapData.name}`">
        </Head>

        <div class="relative w-screen h-screen font-sans bg-gray-50">
            <main ref="mapContainer" class="w-full h-full bg-gray-100"></main>

            <!-- Loading State -->
            <div v-if="isLoading" class="absolute inset-0 bg-white/95 backdrop-blur-sm flex flex-col items-center justify-center z-30">
                <div class="animate-spin rounded-full h-12 w-12 border-2 border-gray-300 border-t-gray-900 mb-4"></div>
                <p class="text-gray-700 text-lg font-medium">Loading Map...</p>
            </div>

            <!-- Error State -->
            <div v-if="error" class="absolute inset-0 bg-white/95 backdrop-blur-sm flex flex-col items-center justify-center z-30 p-8 text-center">
                <div class="w-16 h-16 text-gray-400 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                    </svg>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Unable to Load Map</h2>
                <p class="text-gray-600 max-w-md">{{ error }}</p>
            </div>

            <!-- Map Header -->
            <div v-if="mapData && !isLoading && !error" class="absolute top-0 left-0 right-0 z-20 p-4 pointer-events-none">
                <div class="max-w-4xl mx-auto bg-white/95 backdrop-blur-sm rounded-xl shadow-sm border border-gray-200/50 p-4 flex items-center gap-4 pointer-events-auto">
                    <div class="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                        <SvgIcon :name="mapData.image || 'pin'" :size="24" class="text-gray-700" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h1 class="text-xl font-semibold text-gray-900 truncate">{{ mapData.name }}</h1>
                        <p v-if="mapData.description" class="text-sm text-gray-600 mt-0.5 line-clamp-2">{{ mapData.description }}</p>
                    </div>
                </div>
            </div>

            <!-- Route Controls -->
            <div v-if="!isLoading && !error" class="absolute top-20 left-4 z-20">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-3">
                    <div class="flex flex-col gap-2">
                        <button
                            @click="toggleRouteMode"
                            :class="[
                                'flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200',
                                routeMode 
                                    ? 'bg-blue-600 text-white shadow-sm' 
                                    : 'text-gray-700 hover:bg-gray-100'
                            ]"
                        >
                            <SvgIcon :name="routeMode ? 'clear' : 'route'" :size="18" />
                            <span class="text-sm font-medium">
                                {{ routeMode ? 'Cancel Route' : 'Get Directions' }}
                            </span>
                        </button>
                        
                        <button
                            v-if="routeMode && routePoints.length > 0"
                            @click="clearRoute"
                            class="flex items-center gap-2 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-200"
                        >
                            <SvgIcon name="clear" :size="18" />
                            <span class="text-sm font-medium">Clear Points</span>
                        </button>
                    </div>
                    
                    <!-- Route Instructions -->
                    <div v-if="routeMode && routePoints.length === 0" class="mt-3 p-2 bg-blue-50 rounded-lg">
                        <p class="text-xs text-blue-700">Click on the map to add route points</p>
                    </div>
                    
                    <div v-if="routeMode && routePoints.length === 1" class="mt-3 p-2 bg-yellow-50 rounded-lg">
                        <p class="text-xs text-yellow-700">Click another point to calculate route</p>
                    </div>
                </div>
            </div>

            <!-- Route Information Panel -->
            <div v-if="routeData && !routeError" class="absolute bottom-20 left-4 right-4 md:left-4 md:right-auto md:max-w-sm z-20">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-4">
                    <div class="flex items-center gap-3 mb-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <SvgIcon name="route" :size="18" class="text-blue-600" />
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">Route Information</h3>
                            <p class="text-xs text-gray-500">{{ ROUTE_API_CONFIG.profile }} route</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-3">
                        <div class="text-center p-2 bg-gray-50 rounded-lg">
                            <p class="text-lg font-semibold text-gray-900">{{ formatDistance(routeData.distance) }}</p>
                            <p class="text-xs text-gray-500">Distance</p>
                        </div>
                        <div class="text-center p-2 bg-gray-50 rounded-lg">
                            <p class="text-lg font-semibold text-gray-900">{{ formatDuration(routeData.duration) }}</p>
                            <p class="text-xs text-gray-500">Duration</p>
                        </div>
                    </div>
                    
                    <!-- Turn-by-turn directions -->
                    <div v-if="routeData.legs && routeData.legs[0].steps" class="border-t border-gray-200 pt-3">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Directions</h4>
                        <div class="max-h-40 overflow-y-auto space-y-2">
                            <div 
                                v-for="(step, index) in routeData.legs[0].steps" 
                                :key="index"
                                class="flex items-start gap-2 text-xs"
                            >
                                <div class="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span class="text-blue-600 text-xs font-bold">{{ index + 1 }}</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-700">
                                        {{ step.maneuver.type.charAt(0).toUpperCase() + step.maneuver.type.slice(1) }}
                                        <span v-if="step.name"> on {{ step.name }}</span>
                                        <span v-if="step.maneuver.modifier"> ({{ step.maneuver.modifier }})</span>
                                    </p>
                                    <p class="text-gray-500 mt-0.5">
                                        {{ formatDistance(step.distance) }} • {{ formatDuration(step.duration) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Route Loading -->
            <div v-if="routeLoading" class="absolute bottom-20 left-4 z-20">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-4 flex items-center gap-3">
                    <div class="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-blue-600"></div>
                    <span class="text-sm text-gray-700">Calculating route...</span>
                </div>
            </div>

            <!-- Route Error -->
            <div v-if="routeError" class="absolute bottom-20 left-4 right-4 md:right-auto md:max-w-sm z-20">
                <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div class="flex items-start gap-3">
                        <div class="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12V15z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-red-800">Route Error</h4>
                            <p class="text-sm text-red-700 mt-1">{{ routeError }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Map View Switcher -->
            <div class="absolute bottom-6 right-6 z-20">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-2">
                    <div class="flex flex-col gap-2">
                        <div class="px-2 py-1">
                            <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Map View</span>
                        </div>
                        <div class="flex flex-col gap-1">
                            <button
                                v-for="(theme, name) in UI_CONFIG.mapThemes"
                                :key="name"
                                @click="applyMapStyle(name)"
                                :class="[
                                    'flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 text-left',
                                    selectedTheme === name 
                                        ? 'bg-gray-900 text-white shadow-sm' 
                                        : 'text-gray-700 hover:bg-gray-100'
                                ]"
                            >
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <svg v-if="name === 'Light'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <svg v-if="name === 'Satellite'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 104 0 2 2 0 002-2v-1a2 2 0 012-2h1.945M12 7c-2.761 0-5 2.239-5 5s2.239 5 5 5 5-2.239 5-5-2.239-5-5-5z" />
                                    </svg>
                                </div>
                                <span class="text-sm font-medium">{{ name }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
html, body, #app {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.maplibregl-popup-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 0;
    font-family: inherit;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.maplibregl-popup-close-button {
    color: #9ca3af;
    font-size: 18px;
    padding: 8px;
    margin: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
}
.maplibregl-popup-close-button:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.maplibregl-popup-anchor-bottom .maplibregl-popup-tip {
    border-top-color: white;
}

.custom-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.25));
}

.marker-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1f2937;
    border-radius: 50%;
    padding: 8px;
    transition: all 0.2s ease;
    border: 3px solid white;
    width: 48px;
    height: 48px;
}

.marker-custom-image {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 50%;
}

.custom-marker:hover .marker-icon-container {
    transform: scale(1.1);
    background: #111827;
}

.custom-marker svg {
    fill: #1f2937;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Route mode cursor */
.maplibregl-canvas-container.maplibregl-interactive {
    cursor: crosshair !important;
}

/* Route line animation */
@keyframes route-draw {
    from {
        stroke-dasharray: 1000;
        stroke-dashoffset: 1000;
    }
    to {
        stroke-dasharray: 1000;
        stroke-dashoffset: 0;
    }
}

/* Scrollbar styling for directions */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>