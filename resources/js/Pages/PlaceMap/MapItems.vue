<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { ref, onMounted, computed, defineComponent, h, watch, nextTick } from "vue";
import axios from "axios";
import { debounce } from "lodash";
import maplibregl from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";

// --- Icon Component ---
const MapIcon = defineComponent({
    props: ["name", "size", "class"],
    setup(props) {
        const size = props.size || 20;
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
            plus: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v14m-7-7h14"/>`,
            close: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 6L6 18M6 6l12 12"/>`,
        };
        return () => h("svg", { class: props.class, width: size, height: size, viewBox: "0 0 24 24", fill: "none", innerHTML: iconPaths[props.name] || iconPaths.pin });
    },
});

// --- Shadcn-like Component Styles ---
const Button = "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";
const ButtonPrimary = `${Button} bg-gray-900 text-gray-50 hover:bg-gray-900/90`;
const ButtonSecondary = `${Button} bg-gray-100 text-gray-900 hover:bg-gray-100/80 border`;
const ButtonDestructive = `${Button} bg-red-600 text-white hover:bg-red-700`;
const ButtonGhost = `${Button} hover:bg-gray-100 hover:text-gray-900`;

const Input = "flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50";
const Label = "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70";
const Select = `${Input}`;

// --- Props ---
const props = defineProps({
    placeMapId: Number,
});

// --- State ---
const placeMap = ref(null);
const alert = ref({ show: false, type: "", message: "" });
const placeMapItems = ref([]);
const itemsPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const loading = ref({ details: true, items: true });
const activeTab = ref("details");
const searchQuery = ref("");

const getInitialItemForm = () => ({
    name: "", description: "", address: "", latitude: "", longitude: "",
    image: "pin", type: "place", locationID: null, visibility: "private",
    status: "active", dataItems: [], processing: false, errors: {},
});

const editorState = ref({
    isOpen: false,
    isEditing: false,
    itemId: null,
    form: getInitialItemForm(),
});

const mapIcons = ref([
    { name: "pin", label: "Location Pin" }, { name: "star", label: "Star" },
    { name: "heart", label: "Heart" }, { name: "flag", label: "Flag" },
    { name: "home", label: "Home" }, { name: "work", label: "Work" },
    { name: "cafe", label: "Cafe" }, { name: "park", label: "Park" },
    { name: "restaurant", label: "Restaurant" }, { name: "shopping", label: "Shopping" },
    { name: "hospital", label: "Hospital" }, { name: "school", label: "School" },
]);

const locationSearchQuery = ref("");
const locationSearchResults = ref(null);
const isSearching = ref(false);
const mapContainer = ref(null);
const map = ref(null);
const marker = ref(null);
const showMapEditor = ref(false);

// --- Computed Properties ---
const customFields = computed(() => {
    if (placeMap.value && placeMap.value.customFields) {
        try {
            const fields = typeof placeMap.value.customFields === "string" ? JSON.parse(placeMap.value.customFields) : placeMap.value.customFields;
            return Array.isArray(fields) ? fields : [];
        } catch (e) {
            console.error("Error parsing custom fields:", e);
            return [];
        }
    }
    return [];
});

const formattedSearchResults = computed(() => {
    if (!locationSearchResults.value) return [];
    const { provinces, districts, sectors, cells, villages, healthFacs } = locationSearchResults.value;
    const results = [];
    const addResults = (items, type) => items?.forEach(item => results.push({ ...item, type }));
    addResults(provinces, "Province"); addResults(districts, "District");
    addResults(sectors, "Sector"); addResults(cells, "Cell");
    addResults(villages, "Village"); addResults(healthFacs, "Health Facility");
    return results;
});

// --- Functions ---
const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => (alert.value.show = false), duration);
};

const fetchPlaceMapDetails = async () => {
    loading.value.details = true;
    try {
        const response = await axios.get(route("placeMap.getById", { placeMapId: props.placeMapId }));
        placeMap.value = response.data;
    } catch (error) {
        showAlert("error", "Error fetching map details.");
    } finally {
        loading.value.details = false;
    }
};

const fetchPlaceMapItems = async (page = 1, perPage = itemsPerPage.value) => {
    loading.value.items = true;
    try {
        const response = await axios.get(route("placeMap.getPlaceMapItem", { 
            placeMapId: props.placeMapId, 
            page, 
            perPage,
            searchQuery: searchQuery.value 
        }));
        placeMapItems.value = response.data.data;
        itemsPagination.value = response.data;
    } catch (error) {
        showAlert("error", "Error fetching map items.");
    } finally {
        loading.value.items = false;
    }
};

const openEditorForCreate = () => {
    activeTab.value = 'details';
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
    editorState.value = {
        isOpen: true,
        isEditing: false,
        itemId: null,
        form: {
            ...getInitialItemForm(),
            dataItems: customFields.value.map(f => ({ name: f.name, value: "" })),
        },
    };
    showMapEditor.value = true;
    nextTick(() => initMap());
};

const openEditorForEdit = (item) => {
    activeTab.value = 'details';
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
    const existingDataItems = item.dataItems ? JSON.parse(item.dataItems) : [];
    const existingDataMap = existingDataItems.reduce((acc, curr) => ({ ...acc, [curr.name]: curr.value }), {});

    editorState.value = {
        isOpen: true,
        isEditing: true,
        itemId: item.id,
        form: {
            name: item.name || "",
            description: item.description || "",
            address: item.address || "",
            latitude: item.latitude || "",
            longitude: item.longitude || "",
            image: item.image || "pin",
            type: item.type || "place",
            locationID: item.locationID || null,
            visibility: item.visibility || "private",
            status: item.status || "active",
            dataItems: customFields.value.map(f => ({ name: f.name, value: existingDataMap[f.name] || "" })),
            processing: false,
            errors: {},
        },
    };
    
    if (editorState.value.form.type === 'place' || editorState.value.form.type === 'movingItem') {
        if (item.latitude && item.longitude) {
            showMapEditor.value = true;
            nextTick(() => initMap());
        } else {
            showMapEditor.value = false;
        }
    } else {
        showMapEditor.value = false;
    }
};

const closeEditor = () => {
    editorState.value.isOpen = false;
    showMapEditor.value = false;
    destroyMap();
};

const submitPlaceMapItem = async () => {
    const form = editorState.value.form;
    form.processing = true;
    form.errors = {};
    
    const url = editorState.value.isEditing
        ? route("placeMap.updatePlaceItem", { placeMapId: props.placeMapId, placeMapItemId: editorState.value.itemId })
        : route("placeMap.createPlaceItem", { placeMapId: props.placeMapId });

    try {
        const response = await axios.post(url, form);
        showAlert("success", response.data.message);
        await fetchPlaceMapItems(itemsPagination.value?.currentPage || 1, itemsPerPage.value);
        if (!editorState.value.isEditing) {
            closeEditor();
        }
    } catch (error) {
        const errorMessage = error.response?.data?.message || "An unexpected error occurred.";
        showAlert("error", errorMessage);
        if (error.response?.status === 422) {
            form.errors = error.response.data.errors;
        }
    } finally {
        form.processing = false;
    }
};

const updateMarker = (lat, lng) => {
    if (!map.value) return;
    if (marker.value) {
        marker.value.remove();
    }
    marker.value = new maplibregl.Marker()
        .setLngLat([lng, lat])
        .addTo(map.value);
};

const initMap = () => {
    if (map.value || !mapContainer.value) return;

    const lat = parseFloat(editorState.value.form.latitude);
    const lng = parseFloat(editorState.value.form.longitude);
    
    const initialCenter = [29.8739, -1.9403];
    let initialZoom = 8;

    if (!isNaN(lng) && !isNaN(lat)) {
        initialCenter[0] = lng;
        initialCenter[1] = lat;
        initialZoom = 12;
    }

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                osm: {
                    type: 'raster',
                    tiles: ['https://a.tile.openstreetmap.org/{z}/{x}/{y}.png'],
                    tileSize: 256,
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                }
            },
            layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: {} }]
        },
        center: initialCenter,
        zoom: initialZoom
    });

    map.value.on('load', () => {
        if (!isNaN(lng) && !isNaN(lat)) {
            updateMarker(lat, lng);
        }
    });

    map.value.on('click', (e) => {
        const { lng, lat } = e.lngLat;
        editorState.value.form.latitude = lat.toFixed(6);
        editorState.value.form.longitude = lng.toFixed(6);
    });
};

const destroyMap = () => {
    if (map.value) {
        map.value.remove();
        map.value = null;
    }
    if (marker.value) {
        marker.value = null;
    }
};

const performLocationSearch = debounce(async () => {
    if (locationSearchQuery.value.length < 3) {
        locationSearchResults.value = null;
        return;
    }
    isSearching.value = true;
    try {
        const response = await axios.get(route("api.search"), {
            params: {
                filterData: editorState.value.form.type,
                searchQuery: locationSearchQuery.value,
                lang: "en",
            },
        });
        locationSearchResults.value = response.data;
    } catch (error) {
        showAlert("error", "Failed to search for locations.");
        locationSearchResults.value = null;
    } finally {
        isSearching.value = false;
    }
}, 300);

const selectLocation = (location) => {
    const form = editorState.value.form;
    form.name = location.name;
    form.address = location.address;
    form.locationID = location.id;
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
};

const clearSelectedLocation = () => {
    const form = editorState.value.form;
    form.locationID = null;
    form.name = "";
    form.address = "";
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
};

const enableMapEditing = () => {
    showMapEditor.value = true;
    nextTick(() => initMap());
};

onMounted(async () => {
    await fetchPlaceMapDetails();
    await fetchPlaceMapItems();
});

watch(searchQuery, debounce(() => {
    fetchPlaceMapItems(1, itemsPerPage.value);
}, 300));

watch(itemsPerPage, () => {
    fetchPlaceMapItems(1, itemsPerPage.value);
});

watch(locationSearchQuery, (newValue) => {
    if ((editorState.value.form.type !== "place" && editorState.value.form.type !== "movingItem") && !editorState.value.form.locationID) {
        performLocationSearch();
    }
});

watch(() => editorState.value.form.type, (newType) => {
    if (!editorState.value.isEditing) {
        const form = editorState.value.form;
        form.latitude = "";
        form.longitude = "";
        form.locationID = null;
        form.address = "";
        locationSearchQuery.value = "";
        locationSearchResults.value = null;
    }
    if ((newType === 'place' || newType === 'movingItem') && editorState.value.isOpen) {
        showMapEditor.value = true;
        nextTick(() => initMap());
    } else {
        showMapEditor.value = false;
        destroyMap();
    }
});

watch(
    () => [editorState.value.form.latitude, editorState.value.form.longitude],
    debounce(([newLat, newLng]) => {
        if (map.value && newLat && newLng) {
            const lat = parseFloat(newLat);
            const lng = parseFloat(newLng);
            if (!isNaN(lat) && !isNaN(lng)) {
                updateMarker(lat, lng);
                map.value.flyTo({ center: [lng, lat], zoom: Math.max(map.value.getZoom(), 12) });
            }
        }
    }, 500)
);

</script>

<template>
    <AppLayout :title="placeMap ? `Editing ${placeMap.name}` : 'Map Items'">
        <div class="h-screen-minus-header flex bg-gray-100 font-sans">
            <!-- Left Panel: Item List -->
            <div class="w-[380px] bg-white border-r border-gray-200 flex flex-col">
                <div class="p-4 border-b border-gray-200">
                    <div v-if="placeMap">
                        <div class="flex justify-between items-center mb-2">
                            <Link :href="route('myMap.index')" class="text-sm text-gray-600 hover:text-black">&larr; Back to My Maps</Link>
                            <Link :href="route('myMapData.index', placeMapId)" class="text-sm text-indigo-600 hover:underline">
                                Map Data
                            </Link>
                        </div>
                        <h1 class="text-xl font-bold text-gray-900 truncate">{{ placeMap.name }}</h1>
                        <p class="text-sm text-gray-600 mt-1 truncate">{{ placeMap.description }}</p>
                    </div>
                     <div v-else class="space-y-2">
                        <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div class="h-6 bg-gray-200 rounded w-3/4"></div>
                        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                </div>
                <div class="p-4 border-b border-gray-200 space-y-4">
                    <button @click="openEditorForCreate" :class="`${ButtonPrimary} w-full py-2`">
                        <MapIcon name="plus" size="16" class="mr-2"/> Add New Place
                    </button>
                    <input v-model="searchQuery" :class="Input" placeholder="Search places..." />
                </div>
                <div class="flex-1 overflow-y-auto">
                    <div v-if="loading.items" class="p-4 space-y-3">
                        <div v-for="i in 8" :key="i" class="h-12 bg-gray-200 rounded-md animate-pulse"></div>
                    </div>
                    <div v-else-if="placeMapItems.length > 0">
                        <ul>
                            <li v-for="item in placeMapItems" :key="item.id">
                                <button @click="openEditorForEdit(item)" 
                                        class="w-full text-left p-4 border-b border-gray-200 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 transition-colors duration-150"
                                        :class="{'bg-gray-100': editorState.isOpen && editorState.itemId === item.id}">
                                    <h3 class="font-semibold text-gray-900">{{ item.name }}</h3>
                                    <p class="text-sm text-gray-500 truncate">{{ item.address || 'No address' }}</p>
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div v-else class="text-center p-8 text-gray-500">
                        <p>No places found.</p>
                        <p v-if="searchQuery" class="text-sm">Try a different search term.</p>
                        <p v-else class="text-sm">Click "Add New Place" to start.</p>
                    </div>
                </div>
                
                <!-- Pagination Controls -->
                <div v-if="itemsPagination && itemsPagination.total > 0" class="p-4 border-t border-gray-200 text-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <select v-model="itemsPerPage" :class="`${Select} h-8 text-xs w-auto`">
                                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }} / page</option>
                            </select>
                        </div>
                        <div class="text-gray-600">
                            {{ itemsPagination.pageItems > 0 ? (itemsPagination.currentPage - 1) * itemsPagination.itemsPerPage + 1 : 0 }}-{{ (itemsPagination.currentPage - 1) * itemsPagination.itemsPerPage + itemsPagination.pageItems }} of {{ itemsPagination.total }}
                        </div>
                        <div class="flex items-center space-x-1">
                            <button @click="fetchPlaceMapItems(itemsPagination.currentPage - 1)" :disabled="itemsPagination.currentPage <= 1" :class="`${ButtonGhost} p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed`">Prev</button>
                            <button @click="fetchPlaceMapItems(itemsPagination.currentPage + 1)" :disabled="itemsPagination.currentPage >= itemsPagination.lastPage" :class="`${ButtonGhost} p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed`">Next</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Editor -->
            <main class="flex-1 flex flex-col overflow-hidden">
                <div v-if="!editorState.isOpen" class="flex-1 flex items-center justify-center text-center text-gray-500 bg-gray-50">
                    <div>
                        <MapIcon name="pin" size="48" class="mx-auto text-gray-300"/>
                        <h2 class="mt-4 text-xl font-medium">Select a place to edit</h2>
                        <p>Or create a new one to get started.</p>
                    </div>
                </div>
                <div v-else class="flex-1 flex flex-col bg-gray-50 overflow-y-auto">
                    <!-- Editor Header -->
                    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 flex justify-between items-center sticky top-0 z-10">
                        <h2 class="text-lg font-semibold">{{ editorState.isEditing ? 'Edit Place' : 'New Place' }}</h2>
                        <div class="flex items-center space-x-3">
                            <button @click="submitPlaceMapItem" :disabled="editorState.form.processing" :class="`${ButtonPrimary} px-5 py-2`">
                                {{ editorState.form.processing ? 'Saving...' : (editorState.isEditing ? 'Update' : 'Publish') }}
                            </button>
                            <button @click="closeEditor" :class="`${ButtonGhost} p-2 rounded-full`">
                                <MapIcon name="close" size="20"/>
                            </button>
                        </div>
                    </div>

                    <!-- Editor Form -->
                    <div class="p-8 space-y-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Main Details Card -->
                                <div class="bg-white p-6 rounded-lg border border-gray-200">
                                    <div class="space-y-4">
                                        <div>
                                            <label :class="Label">Type</label>
                                            <select v-model="editorState.form.type" :class="Select">
                                                <option value="place">Place</option>
                                                <option value="movingItem">Moving Item</option>
                                                <option value="province">Province</option>
                                                <option value="district">District</option>
                                                <option value="sector">Sector</option>
                                                <option value="cell">Cell</option>
                                                <option value="village">Village</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label :class="Label">Search Location</label>
                                            <div v-if="editorState.form.locationID" class="flex items-center justify-between h-10 w-full rounded-md border border-gray-200 bg-gray-100 px-3 py-2 text-sm">
                                                <span>{{ editorState.form.name }}</span>
                                                <button type="button" @click="clearSelectedLocation" class="font-medium text-indigo-600 hover:text-indigo-800 hover:underline">Change</button>
                                            </div>
                                            <div v-else class="relative">
                                                <input v-model="locationSearchQuery" placeholder="Search for a location..." :class="Input" />
                                                <div v-if="isSearching" class="absolute right-3 top-2.5 text-gray-400">...</div>
                                                <ul v-if="formattedSearchResults.length > 0" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-auto shadow-lg">
                                                    <li v-for="(result, index) in formattedSearchResults" :key="index" @click="selectLocation(result)" class="px-4 py-3 cursor-pointer hover:bg-gray-100 border-b last:border-b-0">
                                                        <p class="font-semibold text-gray-800">{{ result.name }}</p>
                                                        <p class="text-sm text-gray-500">{{ result.address }}</p>
                                                        <p class="text-xs text-blue-500 font-semibold uppercase tracking-wider mt-1">{{ result.type }}</p>
                                                    </li>
                                                </ul>
                                                <div v-if="!isSearching && locationSearchQuery.length >= 3 && formattedSearchResults.length === 0" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md mt-1 p-4 text-center text-gray-500">
                                                    No results found.
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <label :class="Label">Name</label>
                                            <input v-model="editorState.form.name" :class="Input" required />
                                            <div v-if="editorState.form.errors.name" class="text-red-500 text-sm mt-1">{{ editorState.form.errors.name[0] }}</div>
                                        </div>
                                        <div>
                                            <label :class="Label">Description</label>
                                            <textarea v-model="editorState.form.description" :class="Input" rows="3"></textarea>
                                        </div>
                                        <div>
                                            <label :class="Label">Address</label>
                                            <input v-model="editorState.form.address" :class="Input" />
                                            <div v-if="editorState.form.errors.address" class="text-red-500 text-sm mt-1">{{ editorState.form.errors.address[0] }}</div>
                                        </div>
                                        
                                        <div v-if="editorState.form.type === 'place' || editorState.form.type === 'movingItem'" class="space-y-4">
                                            <div v-if="showMapEditor">
                                                <div class="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <label :class="Label">Latitude</label>
                                                        <input v-model="editorState.form.latitude" :class="Input" placeholder="e.g. -1.9403" />
                                                        <div v-if="editorState.form.errors.latitude" class="text-red-500 text-sm mt-1">{{ editorState.form.errors.latitude[0] }}</div>
                                                    </div>
                                                    <div>
                                                        <label :class="Label">Longitude</label>
                                                        <input v-model="editorState.form.longitude" :class="Input" placeholder="e.g. 29.8739" />
                                                        <div v-if="editorState.form.errors.longitude" class="text-red-500 text-sm mt-1">{{ editorState.form.errors.longitude[0] }}</div>
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <label :class="Label">Or click on the map to select</label>
                                                    <div ref="mapContainer" class="mt-2 h-96 w-full rounded-lg border border-gray-200 bg-gray-100"></div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <label :class="Label">Coordinates</label>
                                                <div class="mt-2">
                                                    <button type="button" @click="enableMapEditing" :class="`${ButtonSecondary} w-full`">
                                                        <MapIcon name="pin" size="16" class="mr-2"/> Add Latitude & Longitude
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Custom Fields Card -->
                                <div v-if="editorState.form.dataItems.length > 0" class="bg-white p-6 rounded-lg border border-gray-200">
                                    <h3 class="text-lg font-medium mb-4">Custom Data</h3>
                                    <div class="space-y-4">
                                        <div v-for="(item, index) in editorState.form.dataItems" :key="index">
                                            <label :class="Label">{{ item.name }}</label>
                                            <input v-model="item.value" :class="Input" :name="`custom_field_${index}`" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Sidebar in Editor -->
                            <div class="lg:col-span-1 space-y-6">
                                <div class="bg-white p-6 rounded-lg border border-gray-200 space-y-4">
                                    <div>
                                        <label :class="Label" for="status">Status</label>
                                        <select v-model="editorState.form.status" :class="Select">
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label :class="Label" for="visibility">Visibility</label>
                                        <select v-model="editorState.form.visibility" :class="Select">
                                            <option value="private">Private</option>
                                            <option value="public">Public</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="bg-white p-6 rounded-lg border border-gray-200 space-y-4">
                                    <label :class="Label" class="block">Icon</label>
                                    <div class="grid grid-cols-5 gap-2">
                                        <button v-for="icon in mapIcons" :key="icon.name" type="button" @click="editorState.form.image = icon.name"
                                                :class="['p-2 rounded-md border-2 flex items-center justify-center', editorState.form.image === icon.name ? 'border-black bg-gray-100' : 'border-gray-200 bg-white hover:border-gray-400']">
                                            <MapIcon :name="icon.name" :size="20" :class="editorState.form.image === icon.name ? 'text-black' : 'text-gray-600'"/>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </AppLayout>
</template>

<style>
.h-screen-minus-header {
    height: calc(100vh - 65px); /* Adjust 65px to your actual header height */
}
</style>

