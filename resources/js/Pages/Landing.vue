<script setup>
import { ref, computed, watch } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import MapBackground from "@/components/MapBackground.vue";
import GlassmorphismSearch from "@/components/GlassmorphismSearch.vue";
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const showFilters = ref(false);
const hoveredResult = ref(null);
const selectedResult = ref(null);
const mapRef = ref(null);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);

// Map configuration
const mapCenter = ref([29.8739, -1.9403]); // Rwanda center
const mapZoom = ref(8.5);
const mapTheme = ref('Black'); // Dark theme for better glassmorphism effect

// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

// --- Functions ---
const performSearch = debounce(async (query, lang) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: 'all',
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );

        // Update map view based on search results
        updateMapView();
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) searchQuery.value = '';
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    hoveredResult.value = null;
    selectedResult.value = null;

    // Reset map view to Rwanda center
    mapCenter.value = [29.8739, -1.9403];
    mapZoom.value = 8.5;
};

const updateMapView = () => {
    if (!hasResults.value) return;

    // Find the first result with coordinates to focus on
    for (const type of ALL_RESULT_TYPES) {
        const items = searchResults.value[type] || [];
        if (items.length > 0) {
            const firstItem = items[0];
            if (firstItem.latitude && firstItem.longitude) {
                mapCenter.value = [firstItem.longitude, firstItem.latitude];
                mapZoom.value = 10;
                break;
            }
        }
    }
};

const handleResultHover = (result) => {
    hoveredResult.value = result;
};

const handleResultClick = (result) => {
    selectedResult.value = result;
    if (result.latitude && result.longitude) {
        mapCenter.value = [result.longitude, result.latitude];
        mapZoom.value = 12;
    }
};

const handleMapReady = (map) => {
    // Map is ready, can perform additional setup if needed
    console.log('Map ready:', map);
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Developer First Geocoding API" />
    <AppLayout>
        <!-- Full Screen Container with Map Background -->
        <div class="relative w-full h-screen overflow-hidden">
            <!-- Background Map -->
            <MapBackground
                ref="mapRef"
                :center="mapCenter"
                :zoom="mapZoom"
                :theme="mapTheme"
                :search-results="searchResults"
                :selected-result="selectedResult"
                :interactive="false"
                @map-ready="handleMapReady"
                class="absolute inset-0 w-full h-full"
            />

            <!-- Glassmorphism Search Interface Overlay -->
            <div class="absolute inset-0 flex items-center justify-center p-4 z-10">
                <GlassmorphismSearch
                    v-model:search-query="searchQuery"
                    v-model:selected-language="selectedLanguage"
                    v-model:show-filters="showFilters"
                    :search-results="searchResults"
                    :is-loading="isLoading"
                    :error="error"
                    :search-time="searchTime"
                    @clear-search="clearSearch"
                    @result-hover="handleResultHover"
                    @result-click="handleResultClick"
                />
            </div>
        </div>

    </AppLayout>
</template>

<style scoped>
/* Ensure full height for the landing page */
.relative {
    min-height: 100vh;
}

/* Smooth transitions for map updates */
.map-transition {
    transition: all 0.8s ease-in-out;
}

/* Custom scrollbar for results in glassmorphism components */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
</style>