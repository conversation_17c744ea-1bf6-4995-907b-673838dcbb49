<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import axios from 'axios';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    provinces: { type: Array, default: () => [] },
});

// --- STATE REFS ---
const mapContainer = ref(null);
const map = ref(null);
const form = ref({
    latitude: '',
    longitude: '',
});
const selectedLanguage = ref(localStorage.getItem('mapLanguage') || 'en');
const panelsVisible = ref(true);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Black');

const searchResults = ref([]);
const lastSearchedCoords = ref(null);

const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const searchLayerIds = ref([]);
const searchSourceIds = ref([]);
const selectedResultId = ref(null);
const selectedResultType = ref(null);
let clickMarker = null;
let hoverPopup = null;
let selectedPopup = null;
let hoveredFeature = { id: null, sourceId: null, type: null };

// --- CONFIGURATION ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403],
    zoom: 8.5,
    fitBoundsPadding: { top: 200, bottom: 200, left: 200, right: 200 },
    maxZoomForFit: 14,
};

const UI_CONFIG = {
    languages: [
        { code: 'rw', name: 'Kinyarwanda' },
        { code: 'en', name: 'English' },
        { code: 'fr', name: 'Français' },
    ],
    layerStyles: {
        province: { fillColor: '#3b82f6', borderColor: '#2563eb', fillOpacity: 0.15, borderWidth: 1.5 },
        searchResult: { fillColor: '#ec4899', borderColor: '#db2777', fillOpacity: 0.2, borderWidth: 0.5 },
        search_center: { fillColor: '#f59e0b', borderColor: '#d97706' },
    },
    highlightStyle: { color: '#ffdd00', width: 3, opacity: 0.9 },
    highlightSourceId: 'highlight-source',
    highlightLayerId: 'highlight-layer',
    mapThemes: {
        'Default': {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.8, 'raster-contrast': 0.2, 'raster-opacity': 0.9 }
        },
        'Black': {
            url: 'https://a.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            paint: {}
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            paint: {}
        }
    },
};

// --- COMPUTED PROPERTIES ---
const totalResults = computed(() => searchResults.value?.length || 0);
const canSearch = computed(() => {
    const lat = parseFloat(form.value.latitude);
    const lon = parseFloat(form.value.longitude);
    return !isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
});

// --- LOGIC FUNCTIONS ---
const submit = () => {
    if (canSearch.value) {
        performSearch(form.value.latitude, form.value.longitude);
    } else {
        error.value = "Please enter valid latitude (-90 to 90) and longitude (-180 to 180).";
    }
};

const performSearch = async (latitude, longitude) => {
    if (!canSearch.value) {
        clearSearch(false);
        return;
    }
    isLoading.value = true;
    error.value = null;
    lastSearchedCoords.value = { latitude, longitude };
    const startTime = performance.now();
    try {
        const { data } = await axios.post('/map/search-latitude-langitude-json', {
            latitude,
            longitude,
            lang: selectedLanguage.value,
        });
        searchResults.value = (data || []).map(item => ({
            ...item,
            geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
        }));
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || `Failed to fetch search results.`;
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
};

const clearSearch = (resetForm = true) => {
    if (resetForm) {
        form.value.latitude = '';
        form.value.longitude = '';
        lastSearchedCoords.value = null;
    }
    searchResults.value = [];
    isLoading.value = false;
    searchTime.value = 0;
    clearSelection();
    updateMapLayers();
};

const updateMapLayers = () => {
    if (!map.value?.isStyleLoaded()) return;

    searchLayerIds.value.forEach(layerId => {
        if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    });
    searchSourceIds.value.forEach(sourceId => {
        if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    });
    searchLayerIds.value = [];
    searchSourceIds.value = [];
    clearHighlight();

    const baseLayerVisibility = totalResults.value > 0 ? 'none' : 'visible';
    props.provinces.forEach(p => {
        if (p?.id && map.value.getSource(`province-source-${p.id}`)) {
            ['fill', 'border'].forEach(suffix => {
                const layerId = `province-${suffix}-${p.id}`;
                if (map.value.getLayer(layerId)) map.value.setLayoutProperty(layerId, 'visibility', baseLayerVisibility);
            });
        }
    });

    const allFeaturesForZoom = [];
    if (totalResults.value > 0) {
        searchResults.value.forEach(result => {
            if (result.geojson) {
                const feature = { type: "Feature", geometry: result.geojson, properties: { ...result, id: result.id, type: 'searchResult' } };
                addGeoJsonLayer('searchResult', result.id, feature, true);
                allFeaturesForZoom.push(feature);
            }
        });

        if (lastSearchedCoords.value) {
            const { latitude, longitude } = lastSearchedCoords.value;
            const pointFeature = { type: "Feature", geometry: { type: "Point", coordinates: [parseFloat(longitude), parseFloat(latitude)] }, properties: { id: 'search-center', type: 'search_center' } };
            addPointMarker('search_center', 'search-center-id', pointFeature, true);
            allFeaturesForZoom.push(pointFeature);
        }

        if (allFeaturesForZoom.length > 0) zoomToResults(allFeaturesForZoom);

    } else if (!form.value.latitude && !form.value.longitude) {
        map.value.flyTo({ center: MAP_CONFIG.center, zoom: MAP_CONFIG.zoom, duration: 1000 });
    }
};

const zoomToResults = (features) => {
    if (!map.value || features.length === 0) return;
    const bounds = new maplibregl.LngLatBounds();
    let hasValidGeometry = false;
    features.forEach(feature => {
        if (feature.geometry?.type === 'Point' && feature.geometry.coordinates) {
            bounds.extend(feature.geometry.coordinates);
            hasValidGeometry = true;
        } else if (feature.geometry?.bbox) {
            bounds.extend(feature.geometry.bbox);
            hasValidGeometry = true;
        } else if (feature.geometry?.coordinates) {
            const extendCoordinates = (coords) => {
                if (Array.isArray(coords[0]) && Array.isArray(coords[0][0])) { // Polygon
                    coords[0].forEach(c => bounds.extend(c));
                } else if (Array.isArray(coords[0])) { // LineString
                    coords.forEach(c => bounds.extend(c));
                } else { // Point
                    bounds.extend(coords);
                }
            };
            extendCoordinates(feature.geometry.coordinates);
            hasValidGeometry = true;
        }
    });
    if (hasValidGeometry && !bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: MAP_CONFIG.maxZoomForFit, duration: 1000 });
    } else if (features.length === 1 && features[0].geometry?.type === 'Point') {
        map.value.flyTo({ center: features[0].geometry.coordinates, zoom: MAP_CONFIG.maxZoomForFit, duration: 800 });
    }
};

const selectResult = (type, id) => {
    const result = searchResults.value.find(r => r.id === id);
    if (result) {
        displayFeaturePopup(result, type);
        if (window.innerWidth < 640) { // sm breakpoint
            panelsVisible.value = false;
        }
    }
};

const showPanels = () => {
    panelsVisible.value = true;
};

const clearSelection = () => {
    selectedResultId.value = null;
    selectedResultType.value = null;
    if (selectedPopup) {
        selectedPopup.remove();
        selectedPopup = null;
    }
    clearHighlight();
};

const clearHighlight = () => {
    if (map.value?.getSource(UI_CONFIG.highlightSourceId)) {
        map.value.getSource(UI_CONFIG.highlightSourceId).setData({ type: 'FeatureCollection', features: [] });
    }
};

const resizeMap = () => map.value?.resize();

const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }
        const paintProperties = theme.paint || UI_CONFIG.mapThemes['Default'].paint;
        for (const key in paintProperties) {
            map.value.setPaintProperty('osm-tiles', key, paintProperties[key]);
        }
        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        return;
    }
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Black'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { osm: { type: 'raster', tiles: [initialTheme.url], tileSize: 256, attribution: initialTheme.attribution } },
                layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: initialTheme.paint }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
        });
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        map.value.on('load', setupMap);
        window.addEventListener('resize', resizeMap);
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', resizeMap);
    map.value?.remove();
});

const setupMap = () => {
    if (!map.value) return;
    if (Array.isArray(props.provinces)) {
        props.provinces.forEach(province => {
            if (province?.id && province.geojson) {
                const feature = {
                    type: "Feature",
                    geometry: typeof province.geojson === 'string' ? JSON.parse(province.geojson) : province.geojson,
                    properties: { ...province, id: province.id, type: 'province' }
                };
                addGeoJsonLayer('province', province.id, feature, false);
            }
        });
    }
    map.value.addSource(UI_CONFIG.highlightSourceId, { type: 'geojson', data: { type: 'FeatureCollection', features: [] } });
    map.value.addLayer({
        id: UI_CONFIG.highlightLayerId,
        type: 'line',
        source: UI_CONFIG.highlightSourceId,
        paint: { 'line-color': UI_CONFIG.highlightStyle.color, 'line-width': UI_CONFIG.highlightStyle.width, 'line-opacity': UI_CONFIG.highlightStyle.opacity },
    });

    applyMapStyle(selectedTheme.value);

    map.value.on('mousemove', handleMapHover);
    map.value.on('mouseleave', handleMapLeave);
    map.value.on('click', handleMapClick);
};

const handleMapLeave = () => {
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    hoveredFeature = { id: null, sourceId: null, type: null };
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
};

const handleMapHover = (e) => {
    if (!map.value?.isStyleLoaded()) return;
    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ].filter(id => !id.includes('-border-'));
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });
    map.value.getCanvas().style.cursor = features.length ? 'pointer' : '';
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
    if (features.length > 0) {
        const topFeature = features[0];
        const featureId = topFeature.properties.id;
        const featureType = topFeature.properties.type;
        const sourceId = topFeature.layer.source;
        if (featureId !== undefined && sourceId && featureType) {
            map.value.setFeatureState({ source: sourceId, id: featureId }, { hover: true });
            hoveredFeature = { id: featureId, sourceId: sourceId, type: featureType };
        }
        const displayName = getDisplayName(topFeature.properties);
        if (displayName) {
            hoverPopup = new maplibregl.Popup({ closeButton: false, anchor: 'bottom-left', offset: [5, -5], className: 'hover-popup' })
                .setLngLat(e.lngLat)
                .setHTML(`<div class="hover-popup-content"><div class="hover-item"><div class="hover-indicator" style="background-color: ${UI_CONFIG.layerStyles[hoveredFeature.type]?.fillColor || '#ccc'}"></div><span>${displayName}</span></div></div>`)
                .addTo(map.value);
        }
    }
};

const handleMapClick = (e) => {
    if (!map.value) return;

    const layersToQuery = searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-')));
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });

    if (features.length > 0) {
        const topFeature = features[0];
        const type = topFeature.properties.type;
        const id = topFeature.properties.id;
        if (id !== undefined && type && type !== 'search_center') {
            let result = searchResults.value.find(r => r.id == id);
            if (result) {
                displayFeaturePopup(result, type);
                return;
            }
        }
    }

    const { lng, lat } = e.lngLat;
    form.value.latitude = lat.toFixed(6);
    form.value.longitude = lng.toFixed(6);

    if (clickMarker) {
        clickMarker.remove();
    }
    clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
        .setLngLat([lng, lat])
        .addTo(map.value);

    submit();
};

const addGeoJsonLayer = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    const layerStyle = UI_CONFIG.layerStyles[type];
    if (!layerStyle) {
        console.warn(`No layer style found for type: ${type}`);
        return;
    }
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: `${type}-fill-${id}`, type: 'fill', source: sourceId,
        paint: { 'fill-color': layerStyle.fillColor, 'fill-opacity': ['case', ['boolean', ['feature-state', 'hover'], false], 0.6, layerStyle.fillOpacity] },
    });
    map.value.addLayer({
        id: `${type}-border-${id}`, type: 'line', source: sourceId,
        paint: { 'line-color': layerStyle.borderColor, 'line-width': ['case', ['boolean', ['feature-state', 'hover'], false], layerStyle.borderWidth + 1, layerStyle.borderWidth] },
    });
    if (trackLayer) {
        searchLayerIds.value.push(`${type}-fill-${id}`, `${type}-border-${id}`);
        searchSourceIds.value.push(sourceId);
    }
};

const addPointMarker = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-point-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    const layerStyle = UI_CONFIG.layerStyles[type];
    if (!layerStyle) {
        console.warn(`No layer style found for type: ${type}`);
        return;
    }
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: `${type}-point-${id}`, type: 'circle', source: sourceId,
        paint: {
            'circle-color': layerStyle.fillColor || '#FF0000',
            'circle-radius': ['case', ['boolean', ['feature-state', 'hover'], false], 8, 6],
            'circle-stroke-color': layerStyle.borderColor || '#FFFFFF',
            'circle-stroke-width': ['case', ['boolean', ['feature-state', 'hover'], false], 2, 1.5],
            'circle-opacity': 0.9,
        },
    });
    if (trackLayer) {
        searchLayerIds.value.push(`${type}-point-${id}`);
        searchSourceIds.value.push(sourceId);
    }
};

const displayFeaturePopup = (feature, type) => {
    if (!map.value) return;
    clearSelection();
    selectedResultId.value = feature.id;
    selectedResultType.value = type;
    const highlightSource = map.value.getSource(UI_CONFIG.highlightSourceId);
    if (highlightSource) {
        if (feature.geojson) highlightSource.setData(feature.geojson);
        else highlightSource.setData({ type: 'FeatureCollection', features: [] });
    }
    const displayName = getDisplayName(feature);
    const popupContent = `
        <div class="selected-popup">
            <div class="popup-header">
                <div class="popup-indicator" style="background-color: ${UI_CONFIG.layerStyles[type]?.fillColor || '#ccc'}"></div>
                <h3>${displayName}</h3>
            </div>
            <div class="popup-content">
                <p class="popup-type">Location</p>
                ${feature.address ? `<p class="popup-address">${feature.address}</p>` : ''}
                ${feature.code ? `<p class="popup-code">Code: ${feature.code}</p>` : ''}
                ${typeof feature.latitude === 'number' && typeof feature.longitude === 'number' ? `<p class="popup-coords">Lat: ${feature.latitude.toFixed(4)}, Lng: ${feature.longitude.toFixed(4)}</p>` : ''}
            </div>
        </div>`;

    const bounds = new maplibregl.LngLatBounds();
    let targetLngLat;

    if (feature.geojson?.bbox) {
        bounds.extend(feature.geojson.bbox);
        targetLngLat = bounds.getCenter();
    } else if (feature.latitude && feature.longitude) {
        targetLngLat = [feature.longitude, feature.latitude];
        bounds.extend(targetLngLat);
    } else {
        targetLngLat = map.value.getCenter();
    }

    if (targetLngLat) {
        selectedPopup = new maplibregl.Popup({ closeButton: true, closeOnClick: false, anchor: 'bottom', offset: [0, -10] })
            .setLngLat(targetLngLat)
            .setHTML(popupContent)
            .addTo(map.value);
        selectedPopup.on('close', clearSelection);

        if (!bounds.isEmpty() && JSON.stringify(bounds.getNorthEast()) !== JSON.stringify(bounds.getSouthWest())) {
             map.value.fitBounds(bounds, { padding: 200, maxZoom: 14, duration: 800 });
        } else {
            map.value.flyTo({ center: targetLngLat, zoom: 14, duration: 800 });
        }
    }
};

const getDisplayName = (result) => {
    return result.name || 'N/A';
};

const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }

    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            form.value.latitude = position.coords.latitude.toFixed(6);
            form.value.longitude = position.coords.longitude.toFixed(6);
            if (clickMarker) clickMarker.remove();
            clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
                .setLngLat([position.coords.longitude, position.coords.latitude])
                .addTo(map.value);
            submit();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
            console.error(err);
        }
    );
};

watch(selectedLanguage, (newLang) => {
    localStorage.setItem('mapLanguage', newLang);
    if (canSearch.value) {
        performSearch(form.value.latitude, form.value.longitude);
    }
});

</script>

<template>
    <AppLayout title="Latitude/Longitude Search">
        <div class="relative w-screen h-screen font-sans">
            <main ref="mapContainer" class="w-full h-full"></main>

            <div v-if="!panelsVisible" class="absolute top-4 left-4 z-20 sm:hidden pointer-events-auto">
                <Button @click="showPanels">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </Button>
            </div>

            <div v-if="panelsVisible" class="absolute top-0 left-0 z-10 h-full w-full sm:w-auto p-2 sm:p-4 flex flex-col gap-4 pointer-events-none">
                <div class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto">
                    <div class="p-4 space-y-3">
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label for="latitude" class="text-xs font-medium text-gray-600">Latitude</label>
                                <Input id="latitude" v-model="form.latitude" type="number" placeholder="-1.9403" class="w-full" @keyup.enter="submit"/>
                            </div>
                            <div>
                                <label for="longitude" class="text-xs font-medium text-gray-600">Longitude</label>
                                <Input id="longitude" v-model="form.longitude" type="number" placeholder="29.8739" class="w-full" @keyup.enter="submit"/>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <Button @click="submit" class="flex-1" :disabled="isLoading || !canSearch">
                                <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Search
                            </Button>
                            <Button @click="getUserLocation" variant="outline" :disabled="isLoading">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </Button>
                             <Dialog>
                                <DialogTrigger as-child>
                                    <Button variant="outline" class="p-2 h-auto">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" />
                                        </svg>
                                    </Button>
                                </DialogTrigger>
                                <DialogContent class="sm:max-w-[425px]">
                                    <DialogHeader>
                                        <DialogTitle>Settings</DialogTitle>
                                        <DialogDescription>
                                            Select language and customize map style.
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div class="grid gap-4 py-4">
                                        <div class="space-y-2">
                                            <label class="text-xs font-semibold text-gray-600 uppercase">Language</label>
                                            <div class="flex items-center bg-gray-100 p-1 rounded-lg">
                                                <button
                                                    v-for="lang in UI_CONFIG.languages"
                                                    :key="lang.code"
                                                    @click="selectedLanguage = lang.code"
                                                    :class="['flex-1 text-center px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200', selectedLanguage === lang.code ? 'bg-white text-blue-600 shadow-sm ring-1 ring-blue-200' : 'text-gray-600 hover:text-gray-800']"
                                                >{{ lang.name }}</button>
                                            </div>
                                        </div>
                                        <div class="space-y-2">
                                            <label class="text-xs font-semibold text-gray-600 uppercase">Map Style</label>
                                            <div class="grid grid-cols-3 gap-2">
                                                <button
                                                    v-for="(theme, name) in UI_CONFIG.mapThemes"
                                                    :key="name"
                                                    @click="applyMapStyle(name)"
                                                    :class="['p-2 text-xs font-medium rounded-lg transition-all duration-200 flex flex-col items-center justify-center gap-1', selectedTheme === name ? 'ring-2 ring-blue-500' : '']"
                                                >
                                                    <div class="w-12 h-8 rounded-md border" :style="{ backgroundColor: name === 'Default' ? '#f0f0f0' : (name === 'Black' ? '#2d3748' : '#34d399') }"></div>
                                                    <span>{{ name }}</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>
                    </div>
                </div>

                <div v-if="canSearch || totalResults > 0 || error" class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto flex flex-col flex-1 min-h-0">
                    <div class="px-4 py-2 border-b border-gray-200/80">
                        <div class="text-sm text-gray-600">
                            <span v-if="isLoading">Searching...</span>
                            <span v-else-if="totalResults > 0">{{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }} <span v-if="searchTime > 0">found in {{ searchTime }}ms</span></span>
                            <span v-else-if="lastSearchedCoords">No results found.</span>
                        </div>
                    </div>

                    <div class="flex-1 overflow-y-auto">
                        <div v-if="error" class="p-4 m-2 text-center text-red-700 bg-red-50 rounded-lg border border-red-200">
                            <p class="font-semibold">An Error Occurred</p>
                            <p class="text-sm mt-1">{{ error }}</p>
                        </div>
                        <div v-else-if="!isLoading && totalResults === 0 && lastSearchedCoords" class="p-6 text-center text-gray-500">
                            <h3 class="font-medium text-gray-900">No results found</h3>
                            <p class="mt-1 text-sm">Try different coordinates.</p>
                        </div>
                        <ul v-else-if="totalResults > 0" class="space-y-1 p-2">
                            <li v-for="result in searchResults" :key="`result-${result.id}`" @click="selectResult('searchResult', result.id)" :class="['p-2.5 hover:bg-blue-50 rounded-lg cursor-pointer transition-colors duration-150 group', selectedResultId === result.id && selectedResultType === 'searchResult' ? 'bg-blue-100 ring-2 ring-blue-200' : '']">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 w-3 h-3 rounded-sm" :style="{ backgroundColor: UI_CONFIG.layerStyles.searchResult.fillColor }"></div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate group-hover:text-blue-800">{{ getDisplayName(result) }}</p>
                                        <p class="text-xs text-gray-500 capitalize truncate">{{ result.address }}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
html, body, #app {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.maplibregl-ctrl-group {
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}
.maplibregl-popup-content {
    padding: 0;
    background: transparent;
    box-shadow: none;
}
.maplibregl-popup.hover-popup .maplibregl-popup-content {
    background-color: rgba(30, 41, 59, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    font-size: 13px;
    max-width: 240px;
}
.maplibregl-popup-anchor-bottom .maplibregl-popup-tip { border-top-color: rgba(30, 41, 59, 0.9); }
.selected-popup {
    background-color: rgba(255, 255, 255, 0.95);
    color: #1f2937;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    border: 1px solid #e5e7eb;
    font-size: 14px;
    max-width: 280px;
}
.selected-popup .popup-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}
.selected-popup .popup-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    flex-shrink: 0;
}
.selected-popup h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}
.selected-popup .popup-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.selected-popup .popup-type, .selected-popup .popup-address, .selected-popup .popup-code, .selected-popup .popup-coords {
    font-size: 12px;
    color: #6b7280;
    margin:0;
}
.hover-popup-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.hover-item {
    display: flex;
    align-items: center;
    gap: 6px;
}
.hover-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}
</style>
