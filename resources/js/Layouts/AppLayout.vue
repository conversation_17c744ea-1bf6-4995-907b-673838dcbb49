<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';

defineProps({
    title: String,
});

const showingNavigationDropdown = ref(false);
const showingPopupMenu = ref(false);

const switchToTeam = (team) => {
    router.put(route('current-team.update'), {
        team_id: team.id,
    }, {
        preserveState: false,
    });
};

const logout = () => {
    router.post(route('logout'));
    showingPopupMenu.value = false;
};

const closePopupMenu = () => {
    showingPopupMenu.value = false;
};

const togglePopupMenu = () => {
    showingPopupMenu.value = !showingPopupMenu.value;
};
</script>

<template>
    <div>

        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-white">
            <!-- Main Navigation -->
            <nav class="bg-white border-b border-gray-200">
                <div class="mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-16">
                        <!-- Logo -->
                        <div class="flex-shrink-0 flex items-center">
                            <div class="flex items-center">
                                <Link :href="route('landing')" class="flex items-center">
                                <ApplicationMark class="text-black" />
                                </Link>
                            </div>

                            <!-- Desktop Navigation Links -->
                            <div class="hidden md:flex md:justify-start space-x-8 px-8">
                               
                                <Link :href="route('search')"
                                    class="text-gray-700 hover:text-black transition-colors duration-200 font-medium"
                                    :class="{ 'text-black border-b-2 border-black': route().current('search') }">
                                Search
                                </Link>
                                 <Link :href="route('search.latitudeLongitude')"
                                    class="text-gray-700 hover:text-black transition-colors duration-200 font-medium"
                                    :class="{ 'text-black border-b-2 border-black': route().current('search.latitudeLongitude') }">
                                Search Lat Lng
                                </Link>
                                <Link :href="route('mapApi.search')"
                                    class="text-gray-700 hover:text-black transition-colors duration-200 font-medium"
                                    :class="{ 'text-black border-b-2 border-black': route().current('mapApi.search') }">
                               API Documentation
                                </Link>
                                  <Link :href="route('myMap.index')"
                                    class="text-gray-700 hover:text-black transition-colors duration-200 font-medium"
                                    :class="{ 'text-black border-b-2 border-black': route().current('myMap.index') }">
                              My Maps
                                </Link>
                            </div>
                        </div>


                        <!-- Menu Button -->
                        <button @click="togglePopupMenu"
                            class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300">
                            <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Popup Menu Overlay -->
            <div v-if="showingPopupMenu"
                class="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
                @click="closePopupMenu"></div>

            <!-- Popup Menu Card -->
            <div v-if="showingPopupMenu" class="fixed inset-0 flex items-center justify-center z-50 p-4">
                <div :class="{ 'scale-100 opacity-100': showingPopupMenu, 'scale-95 opacity-0': !showingPopupMenu }"
                    class="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300">
                    <div class="p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-8">
                            <h2 class="text-2xl font-bold text-white">Menu</h2>
                            <button @click="closePopupMenu"
                                class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <!-- User Section - Not Logged In -->
                        <div v-if="$page.props.auth.user === null" class="mb-8 p-6 bg-gray-50 rounded-xl text-center">
                            <div class="mb-4">
                                <svg class="w-12 h-12 mx-auto text-gray-400 mb-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <p class="text-gray-700 font-medium text-lg">Join the community</p>
                                <p class="text-gray-500 text-sm mt-1">Connect with others and explore more features</p>
                            </div>
                            <div class="space-y-3">
                                <Link :href="route('login')"
                                    class="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors font-medium"
                                    @click="closePopupMenu">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                                <span>Login</span>
                                </Link>
                                <Link :href="route('register')"
                                    class="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gray-100 text-gray-900 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                                    @click="closePopupMenu">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                                <span>Register</span>
                                </Link>
                            </div>
                        </div>

                        <!-- User Section - Logged In -->
                        <div v-if="$page.props.auth.user !== null" class="mb-8 p-6 bg-gray-50 rounded-xl text-center">
                            <div class="mb-4">
                                <img v-if="$page.props.jetstream.managesProfilePhotos"
                                    :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name"
                                    class="w-16 h-16 rounded-full object-cover mx-auto mb-3" />
                                <div>
                                    <p class="font-bold text-gray-900 text-lg">{{ $page.props.auth.user.name }}</p>
                                    <p class="text-gray-500 text-sm">{{ $page.props.auth.user.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="space-y-3 mb-8">
                            
                            <Link :href="route('search')"
                                class="flex items-center space-x-4 px-6 py-4 rounded-xl hover:bg-gray-100 transition-colors text-gray-700 font-medium group"
                                :class="{ 'bg-gray-100 text-black': route().current('search') }"
                                @click="closePopupMenu">
                            <div class="p-2 bg-gray-200 rounded-lg group-hover:bg-gray-300 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                            <span class="text-lg">Search</span>
                            </Link>

                            <!-- Logged In User Links -->
                            <template v-if="$page.props.auth.user !== null">
                                <Link :href="route('dashboard')"
                                class="flex items-center space-x-4 px-6 py-4 rounded-xl hover:bg-gray-100 transition-colors text-gray-700 font-medium group"
                                :class="{ 'bg-gray-100 text-black': route().current('dashboard') }"
                                @click="closePopupMenu">
                            <div class="p-2 bg-gray-200 rounded-lg group-hover:bg-gray-300 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <span class="text-lg">Dashboard</span>
                            </Link>

                                <Link :href="route('profile.show')"
                                    class="flex items-center space-x-4 px-6 py-4 rounded-xl hover:bg-gray-100 transition-colors text-gray-700 font-medium group"
                                    :class="{ 'bg-gray-100 text-black': route().current('profile.show') }"
                                    @click="closePopupMenu">
                                <div class="p-2 bg-gray-200 rounded-lg group-hover:bg-gray-300 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <span class="text-lg">Profile</span>
                                </Link>

                                <Link v-if="$page.props.jetstream.hasApiFeatures" :href="route('api-tokens.index')"
                                    class="flex items-center space-x-4 px-6 py-4 rounded-xl hover:bg-gray-100 transition-colors text-gray-700 font-medium group"
                                    :class="{ 'bg-gray-100 text-black': route().current('api-tokens.index') }"
                                    @click="closePopupMenu">
                                <div class="p-2 bg-gray-200 rounded-lg group-hover:bg-gray-300 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <span class="text-lg">API Tokens</span>
                                </Link>
                            </template>
                        </nav>

                        <!-- Logout Button -->
                        <div v-if="$page.props.auth.user !== null" class="pt-6 border-t border-gray-200">
                            <button @click="logout"
                                class="w-full flex items-center justify-center space-x-3 px-6 py-4 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                <span class="text-lg">Log Out</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Content -->
            <main class="bg-white">
                <slot />
            </main>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar for popup menu */
.popup-menu::-webkit-scrollbar {
    width: 4px;
}

.popup-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.popup-menu::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

.popup-menu::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>