<?php

use App\Models\User;
use App\Models\PlaceMap;
use App\Models\PlaceMapItem;
use App\Models\Cell;
use App\Models\DataMap;
use App\Models\DataMapItem;
use App\Models\District;
use App\Models\Sector;
use App\Models\Village;
use App\Models\Province;

use Laravel\Sanctum\Sanctum;

beforeEach(function () {

    $this->user = User::factory()->create();

    $this->province = Province::factory()->create();
    $this->district = District::factory()->create(['province_id' => $this->province->id]);
    $this->sector = Sector::factory()->create(['district_id' => $this->district->id]);
    $this->cell = Cell::factory()->create(['sector_id' => $this->sector->id]);
    $this->village = Village::factory()->create(['cell_id' => $this->cell->id]);

    Sanctum::actingAs($this->user);

    $this->url = '/map/data';

    $this->placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
    $this->placeMapItem = PlaceMapItem::factory()->create(['place_map_id' => $this->placeMap->id]);
    $this->dataMap = DataMap::factory()->create([
        'place_map_id' => $this->placeMap->id,
        'user_id' => $this->user->id,
        'customFields' => json_encode([
            [
                'name' => 'longitude',
                'type' => 'text',
                'required' => true,
                'default' => '0',
                'label' => 'Longitude',
                'description' => 'The longitude of the data map item',
                'length' => 12,
            ],
            [
                'name' => 'latitude',
                'type' => 'text',
                'required' => true,
                'default' => '0',
                'label' => 'Latitude',
                'description' => 'The latitude of the data map item',
                'length' => 12,
            ],
            [
                'name' => 'timestamp',
                'type' => 'text',
                'required' => false,
                'default' => '0',
                'label' => 'Timestamp',
                'description' => 'The timestamp of the data map item',
                'length' => 255,
            ]
        ])
    ]);
    $this->dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => []
    ]);
});

it('can create data map', function (array $payload) {


    $response = $this->postJson("{$this->url}/{$payload['placeMap']->id}", $payload['payload']);

    $response->assertStatus(200)->assertJson(['message' => 'Data map created successfully']);

    $this->assertDatabaseHas('DataMap', [
        'user_id' => $this->user->id,
        'name' => $payload['payload']['name'],
    ]);

    if (isset($payload['payload']['customFields'])) {
        $dataMap = DataMap::where('user_id', $this->user->id)
            ->where('name', $payload['payload']['name'])
            ->first();

        //  expect($dataMap->customFields)->toEqual( json_encode($payload['payload']['customFields'],true));
    }
})->with([

    function () {

        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

        return [
            'payload' => [
                'name' => 'Test Data Map now',
                'image' => 'pin'
            ],
            'placeMap' => $placeMap
        ];
    },
    function () {

        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

        return [
            'payload' => [
                'name' => 'Test Data Map now',
                'image' => 'pin',
                'customFields' => [
                    [
                        'type' => 'text',
                        'name' => 'field1',
                        'length' => 10,
                        'isRequired' => 'yes'
                    ]
                ],
            ],
            'placeMap' => $placeMap,
        ];
    }
]);

it('can update data map', function (array $payload) {

    $response = $this->putJson("{$this->url}/{$payload['placeId']}/{$payload['dataMapId']}", $payload['payload']);

    $response->assertStatus(200)->assertJson(['message' => 'Data map updated successfully']);

    $this->assertDatabaseHas('DataMap', [
        'user_id' => $this->user->id,
        'name' => $payload['payload']['name'],
        'place_map_id' => $payload['placeId'],
    ]);

    if (isset($payload['payload']['customFields'])) {
        $dataMap = DataMap::where('user_id', $this->user->id)
            ->where('name', $payload['payload']['name'])
            ->where('place_map_id', $payload['placeId'])
            ->first();

        expect($dataMap->customFields)->toEqual($payload['payload']['customFields']);
    }
})->with([

    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

        $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

        return [
            'placeId' => $placeMap->id,
            'dataMapId' => $dataMap->id,
            'payload' => [
                'name' => 'Test Data Map now',
                'image' => 'pin'
            ]
        ];
    },
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);
        return [
            'placeId' => $placeMap->id,
            'dataMapId' => $dataMap->id,
            'payload' => [
                'name' => 'Test Data Map now',
                'image' => 'pin',
                'customFields' => [
                    [
                        'type' => 'text',
                        'name' => 'field1',
                        'length' => 10,
                        'isRequired' => 'yes'
                    ]
                ],
            ]
        ];
    }
]);

it('can get data map by id', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

    $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

    $response = $this->getJson("{$this->url}/{$placeMap->id}/{$dataMap->id}");

    $response->assertStatus(200)->assertJsonStructure([
        'id',
        'name',
    ]);
});

it('can get all data maps', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);

    $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

    $dataMap2 = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

    $dataMap3 = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

    $response = $this->getJson("{$this->url}/{$placeMap->id}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id'
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ])
        ->assertJsonCount(3, 'data');
});

it('can create data map item ', function (array $payload) {


    $response = $this->postJson("{$this->url}/{$payload['dataMapId']}/create-data-item", $payload['payload']);

    $response->assertStatus(200)->assertJson(['message' => 'Data map item created successfully']);

    $this->assertDatabaseHas('DataMapItem', [
        'name' => $payload['payload']['name'],
        'data_map_id' => $payload['dataMapId'],
        'place_map_item_id' => $payload['payload']['placeMapItemID'],

    ]);

    if (isset($payload['payload']['dataItems'])) {
        $dataToValidate = DataMapItem::where('data_map_id', $payload['dataMapId'])
            ->where('place_map_item_id', $payload['payload']['placeMapItemID'])
            ->first();

        $this->assertEquals(json_encode($payload['payload']['dataItems'], true), json_encode($dataToValidate->dataItems));
    }
})->with([

    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        $placeMatItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

        $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

        $data = [
            'name' => 'Test Data Map Item now',
            'placeMapItemID' => $placeMatItem->id,
            'image' => 'pin',
            'type' => 'single',
        ];

        return [
            'placeId' => $placeMap->id,
            'dataMapId' => $dataMap->id,
            'payload' => $data,
        ];
    },
]);

it('can update data map item ', function (array $payload) {

    $response = $this->putJson("{$this->url}/{$payload['dataMapId']}/{$payload['dataMapItemId']}/update-data-item", $payload['payload']);

    $response->assertStatus(200)->assertJson(['message' => 'Data map item updated successfully']);

    $this->assertDatabaseHas('DataMapItem', [
        'name' => $payload['payload']['name'],
        'data_map_id' => $payload['dataMapId'],
        'place_map_item_id' => $payload['payload']['placeMapItemID'],

    ]);

    if (isset($payload['payload']['dataItems'])) {
        $dataToValidate = DataMapItem::where('id', $payload['dataMapItemId'])
            ->where('data_map_id', $payload['dataMapId'])
            ->where('place_map_item_id', $payload['payload']['placeMapItemID'])
            ->first();

        $this->assertEquals(json_encode($payload['payload']['dataItems'], true), json_encode($dataToValidate->dataItems));
    }
})->with([
    function () {
        $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
        $placeMatItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

        $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

        $dataMapItem = DataMapItem::factory()->create(['data_map_id' => $dataMap->id, 'place_map_item_id' => $placeMatItem->id]);
        $placeMapItem2 = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

        $data = [
            'name' => 'Test Data Map Item now',
            'placeMapItemID' => $placeMapItem2->id,
            'image' => 'pin',
            'type' => 'single',
        ];

        return [
            'payload' => $data,
            'dataMapId' => $dataMap->id,
            'dataMapItemId' => $dataMapItem->id
        ];
    },

]);

it('can get all data map items', function () {

    $placeMap = PlaceMap::factory()->create(['user_id' => $this->user->id]);
    $placeMatItem = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $dataMap = DataMap::factory()->create(['place_map_id' => $placeMap->id, 'user_id' => $this->user->id]);

    $dataMapItem = DataMapItem::factory()->create(['data_map_id' => $dataMap->id, 'place_map_item_id' => $placeMatItem->id]);
    $placeMapItem2 = PlaceMapItem::factory()->create(['place_map_id' => $placeMap->id]);

    $dataMapItem2 = DataMapItem::factory()->create(['data_map_id' => $dataMap->id, 'place_map_item_id' => $placeMapItem2->id]);

    $response = $this->getJson("{$this->url}/items/{$dataMap->id}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id'
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ])
        ->assertJsonCount(2, 'data');
});

it('can create data item appending to existing items', function () {
    $dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => [
            [
                'id' => 'item_123',
                'longitude' => '18.9909090',
                'latitude' => '30.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_456',
                'longitude' => '13.9909090',
                'latitude' => '31.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_789',
                'longitude' => '15.9909090',
                'latitude' => '32.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ]
        ]
    ]);

    $payload = [
        'dataMapItemID' => $dataMapItem->id,
        'dataMapID' => $this->dataMap->id,
        'objectID' => null,
        'dataItems' => [
            ['name' => 'longitude', 'value' => '20.123456'],
            ['name' => 'latitude', 'value' => '40.123456']
        ]
    ];

    $response = $this->postJson("{$this->url}/create-custom-data", $payload);

    $response->assertStatus(200)->assertJson(['message' => 'Data created successfully']);

    $dataMapItem = $dataMapItem->fresh();
    expect($dataMapItem->dataItems)->toBeArray()->toHaveCount(4);
    expect($dataMapItem->dataItems[0])->toMatchArray([
        'id' => 'item_123',
        'longitude' => '18.9909090',
        'latitude' => '30.332212',
        'timestamp' => '2023-08-05T14:30:00.000Z'
    ]);
    expect($dataMapItem->dataItems[1])->toMatchArray([
        'id' => 'item_456',
        'longitude' => '13.9909090',
        'latitude' => '31.332212',
        'timestamp' => '2023-08-05T14:30:00.000Z'
    ]);
    expect($dataMapItem->dataItems[2])->toMatchArray([
        'id' => 'item_789',
        'longitude' => '15.9909090',
        'latitude' => '32.332212',
        'timestamp' => '2023-08-05T14:30:00.000Z'
    ]);
    expect($dataMapItem->dataItems[3])->toMatchArray([
        'longitude' => '20.123456',
        'latitude' => '40.123456'
    ])->toHaveKeys(['id', 'timestamp']);

    // Verify unique IDs
    $ids = array_column($dataMapItem->dataItems, 'id');
    expect($ids)->toHaveCount(4);
    expect(array_unique($ids))->toHaveCount(4, 'All data item IDs must be unique');
});

it('can remove data item', function () {
    $dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => [
            [
                'name' => 'longitude',
                'value' => '30.332212',
                'id' => 'item_123',
                'created_at' => now()->toISOString(),
            ],
            [
                'name' => 'latitude',
                'value' => '30.332212',
                'id' => 'item_456',
                'created_at' => now()->toISOString(),
            ]
        ]
    ]);

    $payload = [
        'dataMapID' => $this->dataMap->id,
        'dataMapItemID' => $dataMapItem->id,
        'objectID' => 'item_123'
    ];

    $response = $this->deleteJson("{$this->url}/remove-custom-data", $payload);

    $response->assertStatus(200)->assertJson(['message' => 'Data item removed successfully']);

    $dataMapItem = $dataMapItem->fresh();
    expect($dataMapItem->dataItems)->toBeArray()->toHaveCount(1);
    expect($dataMapItem->dataItems[0])->toMatchArray([
        'name' => 'latitude',
        'value' => '30.332212',
        'id' => 'item_456'
    ])->toHaveKey('created_at');
});


it('fails to remove non-existent data item', function () {
    $payload = [
        'dataMapID' => $this->dataMap->id,
        'dataMapItemID' => $this->dataMapItem->id,
        'objectID' => 'non_existent_id'
    ];

    $response = $this->deleteJson("{$this->url}/remove-custom-data", $payload);

    $response->assertStatus(404)->assertJson(['message' => 'Data item with ID non_existent_id not found']);
});


it('can update data item preserving existing items', function () {
    $dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => [
            [
                'id' => 'item_123',
                'longitude' => '18.9909090',
                'latitude' => '30.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_456',
                'longitude' => '13.9909090',
                'latitude' => '31.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_789',
                'longitude' => '15.9909090',
                'latitude' => '32.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ]
        ]
    ]);

    $payload = [
        'dataMapItemID' => $dataMapItem->id,
        'dataMapID' => $this->dataMap->id,
        'objectID' => 'item_123',
        'dataItems' => [
            ['name' => 'longitude', 'value' => '40.123456'],
            ['name' => 'latitude', 'value' => '50.123456']
        ]
    ];

    $response = $this->putJson("{$this->url}/update-custom-data", $payload);

    $response->assertStatus(200)->assertJson(['message' => 'Data item updated successfully']);

    $dataMapItem = $dataMapItem->fresh();
    expect($dataMapItem->dataItems)->toBeArray()->toHaveCount(3);
    expect($dataMapItem->dataItems[0])->toMatchArray([
        'id' => 'item_123',
        'longitude' => '40.123456',
        'latitude' => '50.123456'
    ])->toHaveKey('timestamp');
    expect($dataMapItem->dataItems[1])->toMatchArray([
        'id' => 'item_456',
        'longitude' => '13.9909090',
        'latitude' => '31.332212',
        'timestamp' => '2023-08-05T14:30:00.000Z'
    ]);
    expect($dataMapItem->dataItems[2])->toMatchArray([
        'id' => 'item_789',
        'longitude' => '15.9909090',
        'latitude' => '32.332212',
        'timestamp' => '2023-08-05T14:30:00.000Z'
    ]);

    // Verify unique IDs
    $ids = array_column($dataMapItem->dataItems, 'id');
    expect($ids)->toHaveCount(3);
    expect(array_unique($ids))->toHaveCount(3, 'All data item IDs must be unique');
});

it('fails to update non-existent data item', function () {
    $dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => [
            [
                'id' => 'item_123',
                'longitude' => '18.9909090',
                'latitude' => '30.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_456',
                'longitude' => '13.9909090',
                'latitude' => '31.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ],
            [
                'id' => 'item_789',
                'longitude' => '15.9909090',
                'latitude' => '32.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z'
            ]
        ]
    ]);
    $payload = [
        'dataMapItemID' => $dataMapItem->id,
        'dataMapID' => $this->dataMap->id,
        'objectID' => '787887ghh',
        'dataItems' => [
            ['name' => 'longitude', 'value' => '40.123456'],
            ['name' => 'latitude', 'value' => '50.123456']
        ]
    ];

    $response = $this->putJson("{$this->url}/update-custom-data", $payload);

    $response->assertStatus(404)->assertJson(['message' => 'Data item with ID 787887ghh not found']);
});

it('can get all data items with pagination', function () {
    $dataMapItem = DataMapItem::factory()->create([
        'data_map_id' => $this->dataMap->id,
        'place_map_item_id' => $this->placeMapItem->id,
        'dataItems' => [
            [
                'longitude' => '18.9909090',
                'latitude' => '30.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z',
                'id' => 'item_123',
            ],
            [
                'longitude' => '13.9909090',
                'latitude' => '31.332212',
                'timestamp' => '2023-08-05T14:30:00.000Z',
                'id' => 'item_456',
            ]
        ]
    ]);

    $perPage = 10;
    $page = 1;
    $datamapID = $this->dataMap->id;

    $response = $this->getJson("{$this->url}/get-custom-data?dataMapID={$datamapID}&dataMapItemID={$dataMapItem->id}&page={$page}&perPage={$perPage}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => ['id', 'longitude', 'latitude', 'timestamp']
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total'
        ])
        ->assertJson(['currentPage' => 1, 'total' => 2]);

    $ids = array_column($response->json('data'), 'id');
    expect($ids)->toHaveCount(2);
    expect(array_unique($ids))->toHaveCount(2);
});

