<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Scout\Searchable;

class DataMap extends Model
{
    /** @use HasFactory<\Database\Factories\DataMapFactory> */
    use HasFactory, Searchable;

    protected $table = 'DataMap';

    protected $fillable = [
        'user_id',
        'place_map_id',
        'name',
        'description',
        'image',
        'key',
        'visibility',
        'customFields',
        'status',
    ];

    protected $casts = [
        'customFields' => 'array',
    ];

    public function placeMap(): BelongsTo
    {
        return $this->belongsTo(PlaceMap::class);
    }

    public function toSearchableArray(): array
    {
        return array_merge($this->toArray(), [
            'id' => (string) $this->id,
            'name' => $this->name,
            'description' => (string) $this->description,
        ]);
    }
}
