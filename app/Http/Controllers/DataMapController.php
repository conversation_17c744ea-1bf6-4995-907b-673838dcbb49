<?php

namespace App\Http\Controllers;

use App\Data\CustomDataDTO;
use App\Data\DataMapData;
use App\Data\DataMapItemData;
use App\Data\RemoveCustomDataDTO;
use App\Http\Requests\CustomDataRequest;
use App\Http\Requests\DataMapItemRequest;
use App\Http\Requests\DataMapRequest;
use App\Http\Requests\RemoveCustomDataRequest;
use App\Services\DataMapService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DataMapController extends Controller
{

    protected $user;
    public function __construct(private DataMapService $service)
    {
        $this->user = Auth::user();
    }

    public function myMapData(int $placeMapId)
    {
        return Inertia::render('PlaceMapData/Index', ['placeMapId' => $placeMapId,]);
    }

    public function myMapDataItems(int $placeMapId, $dataMapId)
    {
        return Inertia::render('PlaceMapData/DataItems', ['placeMapId' => $placeMapId, 'dataMapId' => $dataMapId,]);
    }

    public function index(int $placeMapId)
    {

        $searchQuery = \request()->query('searchQuery', null);
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);

        $result =  $this->service->getDataMap($this->user->id, $placeMapId, perPage: $perPage, page: $page, searchQuery: $searchQuery);
        return  $this->pagination($result);
    }

    public function store(int $placeMapId, DataMapRequest $request)
    {

        $data = DataMapData::from($request->validated());
        $this->service->createDataMap($this->user->id, $placeMapId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data map created successfully'
        ]);
    }

    public function update(int $placeMapId, int $dataMapId, DataMapRequest $request)
    {

        $data = DataMapData::from($request->validated());
        $this->service->updateDataMap($this->user->id, $placeMapId, $dataMapId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data map updated successfully'
        ]);
    }

    public function getDataMapById(int $placeMapId, int $dataMapId)
    {

        return $this->service->getDataMapById($this->user->id, $placeMapId, $dataMapId);
    }

    public function createDataItem(int $dataMapId, DataMapItemRequest $request)
    {

        $data = DataMapItemData::from($request->validated());
        $this->service->createDataMapItem($this->user->id, $dataMapId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data map item created successfully'
        ]);
    }

    public function updateDataItem(int $dataMapId, int $dataMapItemId, DataMapItemRequest $request)
    {

        $data = DataMapItemData::from($request->validated());
        $this->service->updateDataMapItem($this->user->id, $dataMapId, $dataMapItemId, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data map item updated successfully'
        ]);
    }

    public function getDataMapItem(int $dataMapId)
    {

        $searchQuery = \request()->query('searchQuery', null);
        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);

        $result =  $this->service->getDataMapItem($this->user->id, $dataMapId, perPage: $perPage, page: $page, searchQuery: $searchQuery);
        return  $this->pagination($result);
    }

    public function createCustomData(CustomDataRequest $request)
    {

        $data =  CustomDataDTO::from($request->validated());
        $this->service->createCustomData($this->user->id, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data created successfully'
        ]);
    }

    public function removeCustomData(RemoveCustomDataRequest $request)
    {
        $validated = RemoveCustomDataDTO::from($request->validated());
        $this->service->removeDataItem($this->user->id, $validated->dataMapID, $validated->dataMapItemID, $validated->objectID);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data item removed successfully'
        ]);
    }

    public function updateCustomData(CustomDataRequest $request)
    {
        $validated = $request->validated();
        $data = CustomDataDTO::from($validated);
        $this->service->updateDataItem($this->user->id, $data->dataMapItemID, $data->objectID, $data);
        return response()->json([
            'timestamp' => now(),
            'message' => 'Data item updated successfully'
        ]);
    }

    public function getCustomData(){

        $perPage = \request()->query('perPage', 10);
        $page = \request()->query('page', 1);
        $dataMapId = \request()->query('dataMapID', 0);
        $dataMapItemId = \request()->query('dataMapItemID', 0);
        $result = $this->service->getCustomData($this->user->id, perPage: $perPage, page: $page, dataMapId: $dataMapId, dataMapItemId: $dataMapItemId);
        return $this->pagination($result);
    }
}
