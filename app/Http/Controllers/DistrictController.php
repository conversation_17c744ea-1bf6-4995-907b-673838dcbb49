<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\District;

class DistrictController extends Controller
{
    public function index()
    {

        return Cache::remember('Districts', 3600, function () {
            return District::all()->map(function ($district) {
                return [
                    'id' => $district->id,
                    'name' => $district->name,
                    'code' => $district->code,
                    'capture_year' => $district->capture_year,
                    'source' => $district->source,
                    'geojson' => json_decode($district->geojson),
                    'shape_length' => $district->shape_length,
                    'shape_area' => $district->shape_area,
                    'population' => $district->population,
                    'description' => $district->description,
                    'province_id' => $district->province_id,
                ];
            });
        });
    }
}
