<?php

namespace App\Http\Controllers;

use App\Data\SearchData;
use App\Data\SearchGeoJsonData;
use App\Data\SearchLatLongData;
use App\Http\Requests\SearchGeoJsonRequest;
use App\Http\Requests\SearchLatLongRequest;
use App\Http\Requests\SearchRequest;
use App\Services\MapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MapController extends Controller
{
    public function __construct(private MapService $service) {}

    public function index()
    {
        return Inertia::render('map/Index', [
            'provinces' => $this->service->province(),
            'districts' => $this->service->district(),
        ]);
    }

    public function distance(){

        $fromDistrict =10;
        $toDistrict =9;
        $unit = 'km';
        $lang = 'en';
        return $this->service->getDistrictDistance($fromDistrict, $toDistrict, $unit, $lang);
    }

    public function search()
    {

        return Inertia::render('map/Search');
    }

    public function mapApiSearch(){
        return Inertia::render('map/Api');
    }

    public function searchJson(SearchRequest $request)
    {

        $data = SearchData::from($request->validated());
        return $this->service->search($data);
    }

    public function ApiSearch(SearchRequest $request)
    {

        $data = SearchData::from($request->validated());

        return response()->json($this->service->apiSearch($data));
    }

    public function searchLatitudeLongitudeJson(SearchLatLongRequest $request){
        
        $data = SearchLatLongData::from($request->validated());

        return $this->service->searchLatitudeLongitude($data);
    }

    public function searchLatitudeLongitude()
    {

        return Inertia::render('map/SearchLatitudeLongitude');
    }
}
