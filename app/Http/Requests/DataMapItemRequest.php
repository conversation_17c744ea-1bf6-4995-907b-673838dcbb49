<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DataMapItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'placeMapItemID' => 'required|numeric|min:1',
            'customFields' => 'nullable|array',
            'name' => 'required|string|max:80|min:4',
            'description' => 'nullable|string|max:255|min:4',
            'type' => 'required|in:single,multi',
            'visibility' => 'nullable|in:public,private',
            'status' => 'nullable|in:active,inactive',
            'dataItems' => 'nullable|array',
            'dataItems.*.name' => 'required|string|max:80|min:4',
            'dataItems.*.value' => 'required',
        ];
    }
}
