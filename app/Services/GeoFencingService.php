<?php

namespace App\Services;

use App\Models\Cell;
use App\Models\District;
use App\Models\PlaceMapItem;
use App\Models\Province;
use App\Models\Sector;
use App\Models\Village;
use App\Notifications\GeoFenceNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GeoFencingService
{
    /**
     * Check if GPS coordinates trigger any geofencing rules for a place map item
     */
    public function checkGeofencing(PlaceMapItem $placeMapItem, float $latitude, float $longitude, ?string $details = null): void
    {


        $geoFancing = $placeMapItem->geoFancing;

        if (is_string($geoFancing)) {
            $geoFancing = json_decode($geoFancing, true);
        }


        if (!$geoFancing || !isset($geoFancing['geoFancing']) || empty($geoFancing['geoFancing'])) {
            return;
        }
        $geoFancingData = collect($geoFancing['geoFancing'])->keyBy('name')->map(fn($item) => $item['value']);

        $id = $geoFancingData->get('ID');
        $idType = $geoFancingData->get('ID-Type');
        $actionTrigger = $geoFancingData->get('Action-Trigger');
        $type = $geoFancingData->get('Type');
        $radius = $geoFancingData->get('Radius');

        if (empty($id) || empty($idType)) {
            return;
        }

        // Get the geographic entity based on ID and type
        $geoEntity = $this->getGeoEntity($id, $idType);

        if (!$geoEntity) {
            Log::warning("Geofencing entity not found", [
                'id' => $id,
                'type' => $idType,
                'place_map_item_id' => $placeMapItem->id
            ]);
            return;
        }


        // Evaluate geofencing conditions
        $this->evaluateGeofencingConditions(
            $placeMapItem,
            $geoEntity,
            $latitude,
            $longitude,
            $type,
            $radius,
            $details
        );
    }

    /**
     * Get geographic entity by ID and type
     */
    private function getGeoEntity(string $id, string $idType): ?object
    {
        $modelClass = match (strtolower($idType)) {
            'province' => Province::class,
            'district' => District::class,
            'sector' => Sector::class,
            'cell' => Cell::class,
            'village' => Village::class,
            default => null,
        };

        if (!$modelClass) {
            return null;
        }

        return app($modelClass)->find($id);
    }

    /**
     * Evaluate geofencing conditions and trigger notifications
     */
    private function evaluateGeofencingConditions(
        PlaceMapItem $placeMapItem,
        object $geoEntity,
        float $latitude,
        float $longitude,
        ?string $type,
        ?string $radius,
        ?string $details
    ): void {
        $pointWKT = "POINT($longitude $latitude)";
        $isInside = $this->isPointInsideGeometry($pointWKT, $geoEntity);
        $distance = $this->calculateDistanceToGeometry($pointWKT, $geoEntity);

        // Get entity name and type for notification
        $entityName = $this->getEntityName($geoEntity);
        $entityType = class_basename($geoEntity);

        // Determine trigger based on type and conditions
        $triggerType = $this->determineTriggerType($type, $isInside, $distance, $radius);

        if ($triggerType) {
            $this->sendGeofenceNotification(
                $placeMapItem,
                $triggerType,
                $entityName,
                strtolower($entityType),
                $latitude,
                $longitude,
                $distance,
                $details
            );
        }
    }

    /**
     * Check if a point is inside a geometry
     */
    private function isPointInsideGeometry(string $pointWKT, object $geoEntity): bool
    {
        try {
            $result = DB::selectOne(
                "SELECT ST_Contains(geometry, ST_GeomFromText(?, 4326)) as is_inside FROM {$geoEntity->getTable()} WHERE id = ?",
                [$pointWKT, $geoEntity->id]
            );

            return (bool) $result?->is_inside;
        } catch (\Exception $e) {
            Log::error("Error checking point containment", [
                'error' => $e->getMessage(),
                'point' => $pointWKT,
                'entity_id' => $geoEntity->id
            ]);
            return false;
        }
    }

    /**
     * Calculate distance from point to geometry boundary
     */
    private function calculateDistanceToGeometry(string $pointWKT, object $geoEntity): float
    {
        try {
            $result = DB::selectOne(
                "SELECT ST_Distance_Sphere(ST_GeomFromText(?, 4326), geometry) as distance FROM {$geoEntity->getTable()} WHERE id = ?",
                [$pointWKT, $geoEntity->id]
            );

            return (float) ($result?->distance ?? 0);
        } catch (\Exception $e) {
            Log::error("Error calculating distance to geometry", [
                'error' => $e->getMessage(),
                'point' => $pointWKT,
                'entity_id' => $geoEntity->id
            ]);
            return 0.0;
        }
    }

    /**
     * Get entity name for display
     */
    private function getEntityName(object $geoEntity): string
    {
        return match (class_basename($geoEntity)) {
            'Province' => $geoEntity->name_en ?? $geoEntity->name_local ?? 'Unknown Province',
            'District', 'Sector', 'Cell', 'Village' => $geoEntity->name ?? 'Unknown Location',
            default => 'Unknown Location',
        };
    }

    /**
     * Determine trigger type based on conditions
     */
    private function determineTriggerType(?string $type, bool $isInside, float $distance, ?string $radius): ?string
    {
        $radiusMeters = $radius ? (float) $radius * 1000 : 1000; // Convert km to meters, default 1km

        return match (strtolower($type ?? '')) {
            'entered', 'enter' => $isInside ? 'entered' : null,
            'exited', 'exit' => !$isInside ? 'exited' : null,
            'approaching', 'approach' => !$isInside && $distance <= $radiusMeters ? 'approaching' : null,
            'moving_away', 'away' => !$isInside && $distance > $radiusMeters ? 'moving_away' : null,
            default => $isInside ? 'entered' : ($distance <= $radiusMeters ? 'approaching' : null),
        };
    }

    /**
     * Send geofence notification to the place map owner
     */
    private function sendGeofenceNotification(
        PlaceMapItem $placeMapItem,
        string $triggerType,
        string $entityName,
        string $entityType,
        float $latitude,
        float $longitude,
        float $distance,
        ?string $details
    ): void {
        try {
            $user = $placeMapItem->placeMap->user;

            $notification = new GeoFenceNotification(
                $placeMapItem,
                $triggerType,
                $entityName,
                $entityType,
                $latitude,
                $longitude,
                $distance,
                $details
            );

            $user->notify($notification);

            Log::info("Geofence notification sent", [
                'user_id' => $user->id,
                'place_map_item_id' => $placeMapItem->id,
                'trigger_type' => $triggerType,
                'entity_name' => $entityName,
                'latitude' => $latitude,
                'longitude' => $longitude
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to send geofence notification", [
                'error' => $e->getMessage(),
                'place_map_item_id' => $placeMapItem->id,
                'trigger_type' => $triggerType
            ]);
        }
    }
}
